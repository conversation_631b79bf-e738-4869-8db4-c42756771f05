<!DOCTYPE html>
<html>
<head>
    <title>CORS Test</title>
</head>
<body>
    <h1>VT Service CORS Test</h1>
    <button onclick="testVTService()">Test VT Service</button>
    <div id="result"></div>

    <script>
        async function testVTService() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testing...';
            
            try {
                console.log('🧪 Testing VT service...');
                
                const response = await fetch('http://localhost:8083/api/v1/vt/vehicles/simple', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    mode: 'cors',
                });
                
                console.log('📡 Response status:', response.status);
                console.log('📡 Response headers:', response.headers);
                
                const data = await response.json();
                console.log('✅ Success:', data);
                
                resultDiv.innerHTML = `
                    <h3>✅ Success!</h3>
                    <p><strong>Status:</strong> ${response.status}</p>
                    <p><strong>Data:</strong> ${JSON.stringify(data)}</p>
                `;
            } catch (error) {
                console.error('❌ Error:', error);
                resultDiv.innerHTML = `
                    <h3>❌ Error!</h3>
                    <p><strong>Message:</strong> ${error.message}</p>
                    <p><strong>Name:</strong> ${error.name}</p>
                `;
            }
        }
    </script>
</body>
</html>
