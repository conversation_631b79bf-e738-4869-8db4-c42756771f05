server:
  port: 8080

spring:
  application:
    name: api-gateway

  cloud:
    gateway:
      # Global CORS configuration
      globalcors:
        cors-configurations:
          '[/**]':
            allowed-origins:
              - "http://localhost:3000"
              - "http://localhost:3001"
              - "http://127.0.0.1:3000"
              - "http://127.0.0.1:3001"
            allowed-methods:
              - GET
              - POST
              - PUT
              - PATCH
              - DELETE
              - OPTIONS
              - HEAD
            allowed-headers: "*"
            allow-credentials: true
            max-age: 3600

      # Route definitions (can also be defined in Java config)
      routes:
        # Authentication Service
        - id: auth-service
          uri: http://localhost:8082
          predicates:
            - Path=/api/v1/auth/**

        # Driver Profile Service
        - id: driver-profile-service
          uri: http://localhost:8082
          predicates:
            - Path=/api/v1/driverprofile/**
          filters:
            - name: CircuitBreaker
              args:
                name: driver-profile-cb
                fallbackUri: forward:/fallback/profile
            - name: Retry
              args:
                retries: 3
                statuses: BAD_GATEWAY,GATEWAY_TIMEOUT

        # Health check route
        - id: health-check
          uri: http://localhost:8082
          predicates:
            - Path=/actuator/health

        # Future microservices routes
        - id: vehicle-service
          uri: http://localhost:8083
          predicates:
            - Path=/api/v1/vehicles/**
          filters:
            - name: CircuitBreaker
              args:
                name: vehicle-service-cb
                fallbackUri: forward:/fallback/vehicles

        - id: insurance-service
          uri: http://localhost:8084
          predicates:
            - Path=/api/v1/insurance/**
          filters:
            - name: CircuitBreaker
              args:
                name: insurance-service-cb
                fallbackUri: forward:/fallback/insurance

        - id: payment-service
          uri: http://localhost:8085
          predicates:
            - Path=/api/v1/payments/**
          filters:
            - name: CircuitBreaker
              args:
                name: payment-service-cb
                fallbackUri: forward:/fallback/payments

# Actuator configuration
management:
  endpoints:
    web:
      exposure:
        include: health,info,gateway
  endpoint:
    health:
      show-details: always
    gateway:
      enabled: true

# Logging configuration
logging:
  level:
    org.springframework.cloud.gateway: DEBUG
    org.springframework.web.reactive: DEBUG
    reactor.netty: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"

# JWT Configuration (for authentication filter)
jwt:
  secret: e3cb03ba3e9a4f476883618e1cdde7143a6d9fde0af5a779668550c534f6c60e
  expiration: 86400000 # 24 hours
