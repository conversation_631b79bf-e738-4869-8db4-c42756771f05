package whitecape.tech.apigateway.controller;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/gateway")
public class HealthController {

    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> response = new HashMap<>();
        response.put("status", "UP");
        response.put("service", "API Gateway");
        response.put("timestamp", LocalDateTime.now());
        response.put("version", "1.0.0");
        
        return ResponseEntity.ok(response);
    }

    @GetMapping("/info")
    public ResponseEntity<Map<String, Object>> info() {
        Map<String, Object> response = new HashMap<>();
        response.put("name", "TaxStick API Gateway");
        response.put("description", "Central API Gateway for TaxStick Microservices");
        response.put("version", "1.0.0");
        response.put("routes", Map.of(
            "auth", "http://localhost:8082/api/v1/auth/**",
            "profile", "http://localhost:8082/api/v1/driverprofile/**",
            "vehicles", "http://localhost:8083/api/v1/vehicles/**",
            "insurance", "http://localhost:8084/api/v1/insurance/**",
            "payments", "http://localhost:8085/api/v1/payments/**"
        ));
        
        return ResponseEntity.ok(response);
    }
}
