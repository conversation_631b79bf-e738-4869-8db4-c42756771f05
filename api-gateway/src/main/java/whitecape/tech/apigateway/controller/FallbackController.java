package whitecape.tech.apigateway.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/fallback")
@Slf4j
public class FallbackController {

    @GetMapping("/auth")
    @PostMapping("/auth")
    public ResponseEntity<Map<String, Object>> authServiceFallback() {
        log.warn("Auth service is currently unavailable - fallback triggered");
        
        Map<String, Object> response = new HashMap<>();
        response.put("error", "Authentication service is temporarily unavailable");
        response.put("message", "Please try again later");
        response.put("timestamp", LocalDateTime.now());
        response.put("service", "auth-service");
        
        return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body(response);
    }

    @GetMapping("/profile")
    @PostMapping("/profile")
    public ResponseEntity<Map<String, Object>> profileServiceFallback() {
        log.warn("Profile service is currently unavailable - fallback triggered");
        
        Map<String, Object> response = new HashMap<>();
        response.put("error", "Profile service is temporarily unavailable");
        response.put("message", "Please try again later");
        response.put("timestamp", LocalDateTime.now());
        response.put("service", "driver-profile-service");
        
        return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body(response);
    }

    @GetMapping("/vehicles")
    @PostMapping("/vehicles")
    public ResponseEntity<Map<String, Object>> vehicleServiceFallback() {
        log.warn("Vehicle service is currently unavailable - fallback triggered");
        
        Map<String, Object> response = new HashMap<>();
        response.put("error", "Vehicle service is temporarily unavailable");
        response.put("message", "Please try again later");
        response.put("timestamp", LocalDateTime.now());
        response.put("service", "vehicle-service");
        
        return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body(response);
    }

    @GetMapping("/insurance")
    @PostMapping("/insurance")
    public ResponseEntity<Map<String, Object>> insuranceServiceFallback() {
        log.warn("Insurance service is currently unavailable - fallback triggered");
        
        Map<String, Object> response = new HashMap<>();
        response.put("error", "Insurance service is temporarily unavailable");
        response.put("message", "Please try again later");
        response.put("timestamp", LocalDateTime.now());
        response.put("service", "insurance-service");
        
        return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body(response);
    }

    @GetMapping("/payments")
    @PostMapping("/payments")
    public ResponseEntity<Map<String, Object>> paymentServiceFallback() {
        log.warn("Payment service is currently unavailable - fallback triggered");
        
        Map<String, Object> response = new HashMap<>();
        response.put("error", "vignettes service is temporarily unavailable");
        response.put("message", "Please try again later");
        response.put("timestamp", LocalDateTime.now());
        response.put("service", "payment-service");
        
        return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body(response);
    }
}
