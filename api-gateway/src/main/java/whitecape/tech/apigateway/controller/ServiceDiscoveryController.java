package whitecape.tech.apigateway.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.cloud.client.discovery.DiscoveryClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/v1/gateway")
public class ServiceDiscoveryController {

    @Autowired
    private DiscoveryClient discoveryClient;

    @GetMapping("/services")
    public ResponseEntity<Map<String, Object>> getAllServices() {
        Map<String, Object> response = new HashMap<>();
        
        List<String> services = discoveryClient.getServices();
        response.put("services", services);
        response.put("totalServices", services.size());
        
        Map<String, List<ServiceInstance>> serviceInstances = new HashMap<>();
        for (String service : services) {
            List<ServiceInstance> instances = discoveryClient.getInstances(service);
            serviceInstances.put(service, instances);
        }
        response.put("serviceInstances", serviceInstances);
        
        return ResponseEntity.ok(response);
    }

    @GetMapping("/services/{serviceName}")
    public ResponseEntity<Map<String, Object>> getServiceInstances(@PathVariable String serviceName) {
        Map<String, Object> response = new HashMap<>();
        
        List<ServiceInstance> instances = discoveryClient.getInstances(serviceName);
        response.put("serviceName", serviceName);
        response.put("instances", instances);
        response.put("instanceCount", instances.size());
        
        if (instances.isEmpty()) {
            response.put("status", "NO_INSTANCES_AVAILABLE");
        } else {
            response.put("status", "INSTANCES_AVAILABLE");
        }
        
        return ResponseEntity.ok(response);
    }

    @GetMapping("/health")
    public ResponseEntity<Map<String, String>> health() {
        Map<String, String> response = new HashMap<>();
        response.put("status", "UP");
        response.put("service", "API Gateway Service Discovery");
        return ResponseEntity.ok(response);
    }
}
