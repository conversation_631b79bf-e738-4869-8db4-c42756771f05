package whitecape.tech.apigateway.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.gateway.route.Route;
import org.springframework.cloud.gateway.route.RouteLocator;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/v1/gateway/debug")
public class GatewayDebugController {

    @Autowired
    private RouteLocator routeLocator;

    @GetMapping("/routes")
    public ResponseEntity<Map<String, Object>> getRoutes() {
        Map<String, Object> response = new HashMap<>();
        
        Flux<Route> routes = routeLocator.getRoutes();
        List<Map<String, Object>> routeList = routes.map(route -> {
            Map<String, Object> routeInfo = new HashMap<>();
            routeInfo.put("id", route.getId());
            routeInfo.put("uri", route.getUri().toString());
            routeInfo.put("predicate", route.getPredicate().toString());
            routeInfo.put("filters", route.getFilters().toString());
            return routeInfo;
        }).collectList().block();
        
        response.put("routes", routeList);
        response.put("totalRoutes", routeList != null ? routeList.size() : 0);
        
        return ResponseEntity.ok(response);
    }

    @GetMapping("/test-vt")
    public ResponseEntity<Map<String, String>> testVt() {
        Map<String, String> response = new HashMap<>();
        response.put("message", "API Gateway Debug - VT Route Test");
        response.put("vtServiceUrl", "http://localhost:8083");
        response.put("expectedPath", "/api/v1/vt/**");
        response.put("testEndpoint", "/api/v1/vt/vehicles");
        return ResponseEntity.ok(response);
    }
}
