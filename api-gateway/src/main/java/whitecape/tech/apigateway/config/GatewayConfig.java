package whitecape.tech.apigateway.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import reactor.core.publisher.Mono;

@Configuration
@Slf4j
public class GatewayConfig {

    @Bean
    @Order(-1)
    public GlobalFilter loggingFilter() {
        return (exchange, chain) -> {
            String path = exchange.getRequest().getURI().getPath();
            String method = exchange.getRequest().getMethod().name();
            
            log.info("🌐 Gateway Request: {} {}", method, path);
            
            return chain.filter(exchange).then(
                Mono.fromRunnable(() -> {
                    int statusCode = exchange.getResponse().getStatusCode() != null 
                        ? exchange.getResponse().getStatusCode().value() 
                        : 0;
                    log.info("🌐 Gateway Response: {} {} -> {}", method, path, statusCode);
                })
            );
        };
    }

    @Bean
    @Order(0)
    public GlobalFilter requestHeaderFilter() {
        return (exchange, chain) -> {
            // Add custom headers to all requests
            return chain.filter(exchange.mutate()
                .request(exchange.getRequest().mutate()
                    .header("X-Gateway-Request-Id", java.util.UUID.randomUUID().toString())
                    .header("X-Gateway-Timestamp", String.valueOf(System.currentTimeMillis()))
                    .build())
                .build());
        };
    }
}
