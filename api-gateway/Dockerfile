# Multi-stage build for API Gateway
FROM eclipse-temurin:17-jdk-alpine AS build

# Set working directory
WORKDIR /app

# Copy Maven wrapper and pom.xml
COPY .mvn/ .mvn/
COPY mvnw pom.xml ./

# Download dependencies (this layer will be cached if pom.xml doesn't change)
RUN ./mvnw dependency:go-offline -B

# Copy source code
COPY src ./src

# Build the application
RUN ./mvnw clean package -DskipTests

# Extract the JAR file layers
RUN mkdir -p target/dependency && (cd target/dependency; jar -xf ../*.jar)

# Runtime stage
FROM eclipse-temurin:17-jre-alpine

# Add a non-root user
RUN addgroup -g 1001 -S spring && adduser -u 1001 -S spring -G spring

# Set working directory
WORKDIR /app

# Copy the extracted JAR layers from build stage
COPY --from=build --chown=spring:spring /app/target/dependency/BOOT-INF/lib /app/lib
COPY --from=build --chown=spring:spring /app/target/dependency/META-INF /app/META-INF
COPY --from=build --chown=spring:spring /app/target/dependency/BOOT-INF/classes /app

# Switch to non-root user
USER spring:spring

# Expose port
EXPOSE 8080

# Add health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
  CMD wget --no-verbose --tries=1 --spider http://localhost:8080/actuator/health || exit 1

# Run the application
ENTRYPOINT ["java", "-cp", "app:app/lib/*", "whitecape.tech.apigateway.ApiGatewayApplication"]
