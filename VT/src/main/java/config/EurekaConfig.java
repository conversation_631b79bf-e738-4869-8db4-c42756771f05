package config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.netflix.eureka.EurekaInstanceConfigBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

@Configuration
public class EurekaConfig {

    @Value("${server.port:8083}")
    private int serverPort;

    @Value("${spring.application.name:vt-service}")
    private String applicationName;

    @Bean
    public EurekaInstanceConfigBean eurekaInstanceConfig() {
        EurekaInstanceConfigBean config = new EurekaInstanceConfigBean();
        
        // Set custom metadata
        Map<String, String> metadata = new HashMap<>();
        metadata.put("version", "1.0.0");
        metadata.put("description", "Vehicle Technical checkup Service");
        metadata.put("endpoints", "/api/v1/vt");
        metadata.put("swagger-ui", "http://localhost:" + serverPort + "/swagger-ui.html");
        metadata.put("health-check", "http://localhost:" + serverPort + "/actuator/health");
        metadata.put("service-type", "technical-checkup");
        metadata.put("database", "PostgreSQL");
        
        config.setMetadataMap(metadata);
        
        // Set instance preferences
        config.setPreferIpAddress(true);
        config.setLeaseRenewalIntervalInSeconds(10);
        config.setLeaseExpirationDurationInSeconds(30);
        
        return config;
    }
}
