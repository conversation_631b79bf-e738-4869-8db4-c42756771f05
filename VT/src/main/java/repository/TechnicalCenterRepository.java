package repository;

import model.TechnicalCenter;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface TechnicalCenterRepository extends JpaRepository<TechnicalCenter, Integer> {
    
    List<TechnicalCenter> findByIsActiveTrue();
    
    List<TechnicalCenter> findByCity(String city);
    
    @Query("SELECT tc FROM TechnicalCenter tc WHERE tc.isActive = true ORDER BY tc.name")
    List<TechnicalCenter> findActiveCentersOrderByName();
    
    @Query("SELECT tc FROM TechnicalCenter tc WHERE tc.isActive = true AND tc.city = :city ORDER BY tc.name")
    List<TechnicalCenter> findActiveCentersByCity(String city);
}
