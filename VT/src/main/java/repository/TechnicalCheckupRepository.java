package repository;

import model.TechnicalCheckup;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface TechnicalCheckupRepository extends JpaRepository<TechnicalCheckup, Integer> {
    
    List<TechnicalCheckup> findByDriverId(Integer driverId);
    
    List<TechnicalCheckup> findByVehicleId(Integer vehicleId);
    
    List<TechnicalCheckup> findByStatus(TechnicalCheckup.CheckupStatus status);
    
    @Query("SELECT tc FROM TechnicalCheckup tc WHERE tc.driverId = :driverId ORDER BY tc.createdDate DESC")
    List<TechnicalCheckup> findByDriverIdOrderByCreatedDateDesc(@Param("driverId") Integer driverId);
    
    @Query("SELECT tc FROM TechnicalCheckup tc WHERE tc.centerId = :centerId AND tc.appointmentDate = :date")
    List<TechnicalCheckup> findByCenterIdAndAppointmentDate(@Param("centerId") Integer centerId, @Param("date") LocalDate date);
    
    List<TechnicalCheckup> findByVehicleRegistration(String vehicleRegistration);
    
    @Query("SELECT COUNT(tc) FROM TechnicalCheckup tc WHERE tc.centerId = :centerId AND tc.appointmentDate = :date AND tc.status != 'CANCELLED'")
    Long countByCenterIdAndAppointmentDate(@Param("centerId") Integer centerId, @Param("date") LocalDate date);
}
