package model;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.annotation.Id;

import java.time.LocalTime;
import java.util.List;

@Entity
@Table(name = "technical_centers")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class TechnicalCenter {

    @jakarta.persistence.Id
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "name", nullable = false)
    private String name;

    @Column(name = "address")
    private String address;

    @Column(name = "city")
    private String city;

    @Column(name = "postal_code")
    private String postalCode;

    @Column(name = "phone")
    private String phone;

    @Column(name = "email")
    private String email;

    @Column(name = "website")
    private String website;

    @Column(name = "opening_time")
    private LocalTime openingTime;

    @Column(name = "closing_time")
    private LocalTime closingTime;

    @Column(name = "is_active")
    private Boolean isActive = true;

    @ElementCollection
    @CollectionTable(name = "center_working_days", joinColumns = @JoinColumn(name = "center_id"))
    @Column(name = "day_of_week")
    private List<String> workingDays; // MONDAY, TUESDAY, etc.

    @Column(name = "max_appointments_per_day")
    private Integer maxAppointmentsPerDay = 20;

    @Column(name = "appointment_duration_minutes")
    private Integer appointmentDurationMinutes = 60;

    @Column(name = "latitude")
    private Double latitude;

    @Column(name = "longitude")
    private Double longitude;

    @Column(name = "description", length = 1000)
    private String description;

    @ElementCollection
    @CollectionTable(name = "center_services", joinColumns = @JoinColumn(name = "center_id"))
    @Column(name = "service_type")
    private List<String> serviceTypes; // CAR, MOTORCYCLE, TRUCK, etc.

    @Column(name = "rating")
    private Double rating;

    @Column(name = "total_reviews")
    private Integer totalReviews = 0;

    // Helper methods
    public boolean isOpenOnDay(String dayOfWeek) {
        return workingDays != null && workingDays.contains(dayOfWeek.toUpperCase());
    }

    public boolean supportsVehicleType(String vehicleType) {
        return serviceTypes == null || serviceTypes.isEmpty() || 
               serviceTypes.contains(vehicleType.toUpperCase());
    }

    public String getFullAddress() {
        StringBuilder fullAddress = new StringBuilder();
        if (address != null) fullAddress.append(address);
        if (city != null) {
            if (fullAddress.length() > 0) fullAddress.append(", ");
            fullAddress.append(city);
        }
        if (postalCode != null) {
            if (fullAddress.length() > 0) fullAddress.append(" ");
            fullAddress.append(postalCode);
        }
        return fullAddress.toString();
    }


}
