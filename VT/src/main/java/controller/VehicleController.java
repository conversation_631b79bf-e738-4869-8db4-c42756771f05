package controller;

import dto.VehicleDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import service.ServiceToServiceAuthService;
import service.VehicleService;

import java.util.List;

@RestController
@RequestMapping("/api/v1/vt/vehicles")
@CrossOrigin(origins = "*", maxAge = 3600)
public class VehicleController {

    @Autowired
    private VehicleService vehicleService;

    @Autowired
    private ServiceToServiceAuthService serviceToServiceAuthService;

    /**
     * Get driver's vehicles
     */
    @GetMapping
    public ResponseEntity<List<String>> getDriverVehicles(
            @RequestHeader("X-User-Email") String userEmail) {

        System.out.println("🚗 VT Service: Received request for vehicles from user: " + userEmail);

        try {
            // Try to get vehicles from profile service first
            System.out.println("🔗 VT Service: Calling profile service for user: " + userEmail);
            List<String> vehicles = serviceToServiceAuthService.getDriverVehiclesFromProfileService(userEmail);
            System.out.println("✅ VT Service: Successfully got " + vehicles.size() + " vehicles from profile service");
            return ResponseEntity.ok(vehicles);
        } catch (Exception e) {
            System.err.println("❌ VT Service: Failed to get vehicles from profile service: " + e.getMessage());
            e.printStackTrace();
            // Fallback to mock data
            System.out.println("🔄 VT Service: Using fallback mock data");
            Integer driverId = extractDriverIdFromEmail(userEmail);
            List<String> vehicles = vehicleService.getDriverVehicleRegistrations(driverId);
            System.out.println("📋 VT Service: Returning " + vehicles.size() + " mock vehicles");
            return ResponseEntity.ok(vehicles);
        }
    }

    /**
     * Get vehicle details by registration
     */
    @GetMapping("/{registration}")
    public ResponseEntity<VehicleDTO> getVehicleByRegistration(
            @PathVariable String registration,
            @RequestHeader("X-User-Email") String userEmail) {

        Integer driverId = extractDriverIdFromEmail(userEmail);
        VehicleDTO vehicle = vehicleService.getVehicleByRegistration(registration, driverId);

        if (vehicle != null) {
            return ResponseEntity.ok(vehicle);
        } else {
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * Get all vehicles with details
     */
    @GetMapping("/details")
    public ResponseEntity<List<VehicleDTO>> getDriverVehiclesWithDetails(
            @RequestHeader("X-User-Email") String userEmail) {

        Integer driverId = extractDriverIdFromEmail(userEmail);
        List<VehicleDTO> vehicles = vehicleService.getDriverVehicles(driverId);
        return ResponseEntity.ok(vehicles);
    }

    /**
     * Test endpoint without authentication (for debugging)
     */
    @GetMapping("/test")
    public ResponseEntity<Map<String, Object>> testEndpoint() {
        Map<String, Object> response = new HashMap<>();
        response.put("message", "VT Vehicles endpoint is working!");
        response.put("timestamp", java.time.LocalDateTime.now());
        response.put("mockVehicles", java.util.Arrays.asList("ABC123", "XYZ789", "TEST001"));
        return ResponseEntity.ok(response);
    }

    /**
     * Handle OPTIONS requests for CORS preflight
     */
    @RequestMapping(method = RequestMethod.OPTIONS)
    public ResponseEntity<Void> handleOptions() {
        return ResponseEntity.ok().build();
    }

    // Helper method to extract driver ID from email
    private Integer extractDriverIdFromEmail(String email) {
        // Mock implementation - return a fixed driver ID for testing
        return 1;
    }
}
