package controller;

import dto.VehicleDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import service.ServiceToServiceAuthService;
import service.VehicleService;

import java.util.List;

@RestController
@RequestMapping("/api/v1/vt/vehicles")
public class VehicleController {

    @Autowired
    private VehicleService vehicleService;

    /**
     * Get driver's vehicles
     */
    @GetMapping
    public ResponseEntity<List<String>> getDriverVehicles(
            @RequestHeader("X-User-Email") String userEmail) {

        Integer driverId = extractDriverIdFromEmail(userEmail);
        List<String> vehicles = vehicleService.getDriverVehicleRegistrations(driverId);
        return ResponseEntity.ok(vehicles);
    }

    /**
     * Get vehicle details by registration
     */
    @GetMapping("/{registration}")
    public ResponseEntity<VehicleDTO> getVehicleByRegistration(
            @PathVariable String registration,
            @RequestHeader("X-User-Email") String userEmail) {

        Integer driverId = extractDriverIdFromEmail(userEmail);
        VehicleDTO vehicle = vehicleService.getVehicleByRegistration(registration, driverId);

        if (vehicle != null) {
            return ResponseEntity.ok(vehicle);
        } else {
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * Get all vehicles with details
     */
    @GetMapping("/details")
    public ResponseEntity<List<VehicleDTO>> getDriverVehiclesWithDetails(
            @RequestHeader("X-User-Email") String userEmail) {

        Integer driverId = extractDriverIdFromEmail(userEmail);
        List<VehicleDTO> vehicles = vehicleService.getDriverVehicles(driverId);
        return ResponseEntity.ok(vehicles);
    }

    // Helper method to extract driver ID from email
    private Integer extractDriverIdFromEmail(String email) {
        // Mock implementation - return a fixed driver ID for testing
        return 1;
    }
}
