package controller;

import dto.CreateCheckupRequest;
import dto.TechnicalCheckupDTO;
import model.TechnicalCheckup;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import service.TechnicalCheckupService;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/v1/vt/checkups")
@CrossOrigin(origins = {"http://localhost:3000", "http://localhost:3001"})
public class TechnicalCheckupController {

    @Autowired
    private TechnicalCheckupService checkupService;

    /**
     * Create a new technical checkup request
     */
    @PostMapping
    public ResponseEntity<TechnicalCheckupDTO> createCheckup(
            @RequestBody CreateCheckupRequest request,
            @RequestHeader("X-User-Email") String userEmail) {
        
        // Extract driver ID from user email or authentication context
        // For now, using a mock driver ID
        Integer driverId = extractDriverIdFromEmail(userEmail);
        
        TechnicalCheckupDTO checkup = checkupService.createCheckup(request, driverId);
        return ResponseEntity.ok(checkup);
    }

    /**
     * Get driver's checkup history
     */
    @GetMapping("/history")
    public ResponseEntity<List<TechnicalCheckupDTO>> getCheckupHistory(
            @RequestHeader("X-User-Email") String userEmail) {
        
        Integer driverId = extractDriverIdFromEmail(userEmail);
        List<TechnicalCheckupDTO> history = checkupService.getDriverCheckupHistory(driverId);
        return ResponseEntity.ok(history);
    }

    /**
     * Get all checkups for current driver
     */
    @GetMapping
    public ResponseEntity<List<TechnicalCheckupDTO>> getDriverCheckups(
            @RequestHeader("X-User-Email") String userEmail) {
        
        Integer driverId = extractDriverIdFromEmail(userEmail);
        List<TechnicalCheckupDTO> checkups = checkupService.getDriverCheckups(driverId);
        return ResponseEntity.ok(checkups);
    }

    /**
     * Get checkup by ID
     */
    @GetMapping("/{checkupId}")
    public ResponseEntity<TechnicalCheckupDTO> getCheckupById(
            @PathVariable Integer checkupId,
            @RequestHeader("X-User-Email") String userEmail) {
        
        TechnicalCheckupDTO checkup = checkupService.getCheckupById(checkupId);
        return ResponseEntity.ok(checkup);
    }

    /**
     * Schedule appointment for checkup
     */
    @PostMapping("/{checkupId}/schedule")
    public ResponseEntity<TechnicalCheckupDTO> scheduleAppointment(
            @PathVariable Integer checkupId,
            @RequestBody Map<String, String> scheduleRequest,
            @RequestHeader("X-User-Email") String userEmail) {
        
        Integer centerId = Integer.parseInt(scheduleRequest.get("centerId"));
        String appointmentDate = scheduleRequest.get("appointmentDate");
        String appointmentTime = scheduleRequest.get("appointmentTime");
        
        TechnicalCheckupDTO checkup = checkupService.scheduleAppointment(
            checkupId, centerId, appointmentDate, appointmentTime);
        
        return ResponseEntity.ok(checkup);
    }

    /**
     * Cancel checkup
     */
    @DeleteMapping("/{checkupId}")
    public ResponseEntity<Map<String, String>> cancelCheckup(
            @PathVariable Integer checkupId,
            @RequestHeader("X-User-Email") String userEmail) {
        
        Integer driverId = extractDriverIdFromEmail(userEmail);
        checkupService.cancelCheckup(checkupId, driverId);
        
        Map<String, String> response = new HashMap<>();
        response.put("message", "Checkup cancelled successfully");
        
        return ResponseEntity.ok(response);
    }

    /**
     * Update checkup status (admin function)
     */
    @PatchMapping("/{checkupId}/status")
    public ResponseEntity<TechnicalCheckupDTO> updateCheckupStatus(
            @PathVariable Integer checkupId,
            @RequestBody Map<String, String> statusRequest) {
        
        String status = statusRequest.get("status");
        TechnicalCheckup.CheckupStatus checkupStatus = TechnicalCheckup.CheckupStatus.valueOf(status);
        
        TechnicalCheckupDTO checkup = checkupService.updateCheckupStatus(checkupId, checkupStatus);
        return ResponseEntity.ok(checkup);
    }

    /**
     * Complete checkup with result (admin function)
     */
    @PostMapping("/{checkupId}/complete")
    public ResponseEntity<TechnicalCheckupDTO> completeCheckup(
            @PathVariable Integer checkupId,
            @RequestBody Map<String, String> completionRequest) {
        
        String result = completionRequest.get("result");
        String certificateNumber = completionRequest.get("certificateNumber");
        
        TechnicalCheckupDTO checkup = checkupService.completeCheckup(checkupId, result, certificateNumber);
        return ResponseEntity.ok(checkup);
    }

    // Helper method to extract driver ID from email
    // In a real implementation, this would query the user service
    private Integer extractDriverIdFromEmail(String email) {
        // Mock implementation - return a fixed driver ID for testing
        return 1;
    }
}
