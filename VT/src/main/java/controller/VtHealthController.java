package controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.netflix.eureka.EurekaDiscoveryClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import repository.TechnicalCenterRepository;
import repository.TechnicalCheckupRepository;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/v1/vt")
public class VtHealthController {

    @Autowired
    private TechnicalCenterRepository centerRepository;

    @Autowired
    private TechnicalCheckupRepository checkupRepository;

    @Autowired(required = false)
    private EurekaDiscoveryClient discoveryClient;

    @Value("${eureka.instance.appName:vt-service}")
    private String serviceName;

    @Value("${server.port:8083}")
    private int serverPort;

    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> response = new HashMap<>();
        response.put("status", "UP");
        response.put("service", "VT (Vehicle Technical checkup) Service");
        response.put("timestamp", LocalDateTime.now());
        response.put("version", "1.0.0");

        // Add database statistics
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalCenters", centerRepository.count());
        stats.put("totalCheckups", checkupRepository.count());
        response.put("statistics", stats);

        // Add Eureka information
        Map<String, Object> eurekaInfo = new HashMap<>();
        eurekaInfo.put("serviceName", serviceName);
        eurekaInfo.put("port", serverPort);
        eurekaInfo.put("registeredWithEureka", discoveryClient != null);
        if (discoveryClient != null) {
            eurekaInfo.put("eurekaServerUrl", "http://localhost:8761");
            eurekaInfo.put("instanceId", serviceName + ":" + serverPort);
        }
        response.put("eureka", eurekaInfo);

        // Add CORS information
        Map<String, Object> corsInfo = new HashMap<>();
        corsInfo.put("enabled", true);
        corsInfo.put("allowedOrigins", "http://localhost:3000, http://localhost:3001, http://localhost:8080");
        corsInfo.put("allowedMethods", "GET, POST, PUT, PATCH, DELETE, OPTIONS, HEAD");
        corsInfo.put("allowCredentials", true);
        response.put("cors", corsInfo);

        return ResponseEntity.ok(response);
    }

    @GetMapping("/info")
    public ResponseEntity<Map<String, Object>> info() {
        Map<String, Object> response = new HashMap<>();
        response.put("name", "VT Service");
        response.put("description", "Vehicle Technical checkup Management Service");
        response.put("version", "1.0.0");
        response.put("port", "8083");
        response.put("status", "RUNNING");

        Map<String, String> endpoints = new HashMap<>();
        endpoints.put("checkups", "/api/v1/vt/checkups");
        endpoints.put("centers", "/api/v1/vt/centers");
        endpoints.put("vehicles", "/api/v1/vt/vehicles");
        endpoints.put("health", "/api/v1/vt/health");
        endpoints.put("cors-test", "/api/v1/vt/cors-test/simple");

        response.put("endpoints", endpoints);
        response.put("timestamp", LocalDateTime.now());

        return ResponseEntity.ok(response);
    }

    @GetMapping("/test")
    public ResponseEntity<Map<String, String>> test() {
        Map<String, String> response = new HashMap<>();
        response.put("message", "VT Service is working!");
        response.put("timestamp", LocalDateTime.now().toString());
        response.put("service", "vt-service");
        response.put("port", "8083");
        return ResponseEntity.ok(response);
    }
}
