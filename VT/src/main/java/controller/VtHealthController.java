package controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.netflix.eureka.EurekaDiscoveryClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import repository.TechnicalCenterRepository;
import repository.TechnicalCheckupRepository;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/v1/vt")
public class VtHealthController {

    @Autowired
    private TechnicalCenterRepository centerRepository;

    @Autowired
    private TechnicalCheckupRepository checkupRepository;

    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> response = new HashMap<>();
        response.put("status", "UP");
        response.put("service", "VT (Vehicle Technical checkup) Service");
        response.put("timestamp", LocalDateTime.now());
        response.put("version", "1.0.0");

        // Add database statistics
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalCenters", centerRepository.count());
        stats.put("totalCheckups", checkupRepository.count());
        response.put("statistics", stats);

        return ResponseEntity.ok(response);
    }

    @GetMapping("/info")
    public ResponseEntity<Map<String, Object>> info() {
        Map<String, Object> response = new HashMap<>();
        response.put("name", "VT Service");
        response.put("description", "Vehicle Technical checkup Management Service");
        response.put("version", "1.0.0");

        Map<String, String> endpoints = new HashMap<>();
        endpoints.put("checkups", "/api/v1/vt/checkups");
        endpoints.put("centers", "/api/v1/vt/centers");
        endpoints.put("vehicles", "/api/v1/vt/vehicles");
        endpoints.put("health", "/api/v1/vt/health");

        response.put("endpoints", endpoints);
        response.put("timestamp", LocalDateTime.now());

        return ResponseEntity.ok(response);
    }
}
