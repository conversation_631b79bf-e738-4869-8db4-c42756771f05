package service;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.Arrays;
import java.util.List;

@Service
public class ServiceToServiceAuthService {

    private final RestTemplate restTemplate;

    @Value("${service.auth.internal-token:vt-service-internal-token}")
    private String internalServiceToken;

    private static final String PROFILE_SERVICE_URL = "http://localhost:8080/api/v1/internal";

    public ServiceToServiceAuthService(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

    /**
     * Get driver vehicles from profile service using service-to-service authentication
     */
    public List<String> getDriverVehiclesFromProfileService(String userEmail) {
        try {
            String url = PROFILE_SERVICE_URL + "/driver/vehicles";

            HttpHeaders headers = new HttpHeaders();
            headers.set("X-User-Email", userEmail);
            headers.set("X-Service-Token", internalServiceToken);
            headers.set("X-Requesting-Service", "vt-service");

            HttpEntity<String> entity = new HttpEntity<>(headers);

            ResponseEntity<String[]> response = restTemplate.exchange(
                url, HttpMethod.GET, entity, String[].class);

            if (response.getBody() != null) {
                return Arrays.asList(response.getBody());
            }

            return Arrays.asList();
        } catch (Exception e) {
            System.err.println("Error calling profile service: " + e.getMessage());
            e.printStackTrace();
            throw new RuntimeException("Failed to fetch vehicles from profile service: " + e.getMessage());
        }
    }

    /**
     * Get vehicle details from profile service
     */
    public Object getVehicleDetailsFromProfileService(String registration, String userEmail) {
        try {
            String url = PROFILE_SERVICE_URL + "/vehicles/" + registration;

            HttpHeaders headers = new HttpHeaders();
            headers.set("X-User-Email", userEmail);
            headers.set("X-Service-Token", internalServiceToken);
            headers.set("X-Requesting-Service", "vt-service");

            HttpEntity<String> entity = new HttpEntity<>(headers);

            ResponseEntity<Object> response = restTemplate.exchange(
                url, HttpMethod.GET, entity, Object.class);

            return response.getBody();
        } catch (Exception e) {
            System.err.println("Error getting vehicle details from profile service: " + e.getMessage());
            throw new RuntimeException("Failed to fetch vehicle details: " + e.getMessage());
        }
    }
}
