package service;

import dto.VehicleDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Service
public class VehicleServiceImpl implements VehicleService {

    @Autowired
    private RestTemplate restTemplate;
    
    // URL of the profile service (through API Gateway)
    private static final String PROFILE_SERVICE_URL = "http://localhost:8080/api/v1/driverprofile";

    @Override
    public List<String> getDriverVehicleRegistrations(Integer driverId) {
        try {
            // Call the profile service to get vehicle registrations
            String url = PROFILE_SERVICE_URL + "/vehicles";
            
            HttpHeaders headers = new HttpHeaders();
            // Add authentication headers if needed
            // headers.set("Authorization", "Bearer " + token);
            
            HttpEntity<String> entity = new HttpEntity<>(headers);
            
            ResponseEntity<String[]> response = restTemplate.exchange(
                url, HttpMethod.GET, entity, String[].class);
            
            if (response.getBody() != null) {
                return Arrays.asList(response.getBody());
            }
            
            return new ArrayList<>();
        } catch (Exception e) {
            System.err.println("Error fetching vehicle registrations: " + e.getMessage());
            // Return mock data for testing
            return getMockVehicleRegistrations();
        }
    }

    @Override
    public VehicleDTO getVehicleByRegistration(String registration, Integer driverId) {
        try {
            // For now, return mock data based on registration
            // In a real implementation, this would call the profile service or vehicle service
            return getMockVehicleDetails(registration, driverId);
        } catch (Exception e) {
            System.err.println("Error fetching vehicle details: " + e.getMessage());
            return getMockVehicleDetails(registration, driverId);
        }
    }

    @Override
    public List<VehicleDTO> getDriverVehicles(Integer driverId) {
        List<String> registrations = getDriverVehicleRegistrations(driverId);
        List<VehicleDTO> vehicles = new ArrayList<>();
        
        for (String registration : registrations) {
            VehicleDTO vehicle = getVehicleByRegistration(registration, driverId);
            if (vehicle != null) {
                vehicles.add(vehicle);
            }
        }
        
        return vehicles;
    }

    // Mock data methods for testing
    private List<String> getMockVehicleRegistrations() {
        return Arrays.asList("ABC123", "XYZ789", "DEF456");
    }

    private VehicleDTO getMockVehicleDetails(String registration, Integer driverId) {
        VehicleDTO vehicle = new VehicleDTO();
        vehicle.setRegistration(registration);
        vehicle.setImmat(registration);
        vehicle.setDriverId(driverId);
        
        // Mock data based on registration
        switch (registration) {
            case "ABC123":
                vehicle.setVehicleType("Car");
                vehicle.setVehicleModel("Toyota Corolla");
                vehicle.setVehicleYear("2020");
                vehicle.setChassisNumber("1HGBH41JXMN109186");
                vehicle.setVehicleColor("White");
                vehicle.setVehicleFuelType("Petrol");
                break;
            case "XYZ789":
                vehicle.setVehicleType("Car");
                vehicle.setVehicleModel("Honda Civic");
                vehicle.setVehicleYear("2019");
                vehicle.setChassisNumber("2HGFC2F59HH123456");
                vehicle.setVehicleColor("Blue");
                vehicle.setVehicleFuelType("Hybrid");
                break;
            case "DEF456":
                vehicle.setVehicleType("Motorcycle");
                vehicle.setVehicleModel("Yamaha R1");
                vehicle.setVehicleYear("2021");
                vehicle.setChassisNumber("JYARN23E0LA123456");
                vehicle.setVehicleColor("Black");
                vehicle.setVehicleFuelType("Petrol");
                break;
            default:
                vehicle.setVehicleType("Car");
                vehicle.setVehicleModel("Unknown Model");
                vehicle.setVehicleYear("2020");
                vehicle.setChassisNumber("UNKNOWN");
                vehicle.setVehicleColor("Unknown");
                vehicle.setVehicleFuelType("Petrol");
        }
        
        return vehicle;
    }
}
