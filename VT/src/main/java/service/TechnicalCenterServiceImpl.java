package service;

import dto.AvailableSlotDTO;
import dto.TechnicalCenterDTO;
import model.AvailableSlot;
import model.TechnicalCenter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import repository.AvailableSlotRepository;
import repository.TechnicalCenterRepository;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Transactional
public class TechnicalCenterServiceImpl implements TechnicalCenterService {

    @Autowired
    private TechnicalCenterRepository centerRepository;
    
    @Autowired
    private AvailableSlotRepository slotRepository;

    @Override
    @Transactional(readOnly = true)
    public List<TechnicalCenterDTO> getAllActiveCenters() {
        List<TechnicalCenter> centers = centerRepository.findActiveCentersOrderByName();
        return centers.stream()
                .map(TechnicalCenterDTO::new)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<TechnicalCenterDTO> getCentersByCity(String city) {
        List<TechnicalCenter> centers = centerRepository.findActiveCentersByCity(city);
        return centers.stream()
                .map(TechnicalCenterDTO::new)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public TechnicalCenterDTO getCenterById(Integer centerId) {
        TechnicalCenter center = centerRepository.findById(centerId)
                .orElseThrow(() -> new RuntimeException("Technical center not found with id: " + centerId));
        return new TechnicalCenterDTO(center);
    }

    @Override
    @Transactional(readOnly = true)
    public List<AvailableSlotDTO> getAvailableDates(Integer centerId) {
        // Get available slots for the next 30 days
        LocalDate startDate = LocalDate.now();
        LocalDate endDate = startDate.plusDays(30);
        
        List<AvailableSlot> slots = slotRepository.findAvailableSlotsByCenterAndDateRange(centerId, startDate, endDate);
        return slots.stream()
                .map(AvailableSlotDTO::new)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<AvailableSlotDTO> getAvailableSlots(Integer centerId, LocalDate date) {
        List<AvailableSlot> slots = slotRepository.findByCenterIdAndDateAndIsAvailableTrue(centerId, date);
        return slots.stream()
                .map(AvailableSlotDTO::new)
                .collect(Collectors.toList());
    }

    @Override
    public AvailableSlotDTO bookSlot(Integer slotId, Integer driverId, Integer checkupId) {
        AvailableSlot slot = slotRepository.findById(slotId)
                .orElseThrow(() -> new RuntimeException("Available slot not found with id: " + slotId));
        
        if (!slot.getIsAvailable()) {
            throw new RuntimeException("This slot is no longer available");
        }
        
        slot.setIsAvailable(false);
        slot.setBookedByDriverId(driverId);
        slot.setCheckupId(checkupId);
        
        AvailableSlot bookedSlot = slotRepository.save(slot);
        return new AvailableSlotDTO(bookedSlot);
    }

    @Override
    public void generateAvailableSlots(Integer centerId, LocalDate startDate, LocalDate endDate) {
        TechnicalCenter center = centerRepository.findById(centerId)
                .orElseThrow(() -> new RuntimeException("Technical center not found with id: " + centerId));
        
        List<AvailableSlot> slots = new ArrayList<>();
        
        LocalDate currentDate = startDate;
        while (!currentDate.isAfter(endDate)) {
            // Check if this day is a working day
            String dayOfWeek = currentDate.getDayOfWeek().name();
            if (center.getWorkingDays() != null && center.getWorkingDays().contains(dayOfWeek)) {
                // Generate time slots for this day
                LocalTime currentTime = center.getOpeningTime();
                LocalTime closingTime = center.getClosingTime();
                
                while (currentTime.isBefore(closingTime)) {
                    AvailableSlot slot = new AvailableSlot();
                    slot.setCenterId(centerId);
                    slot.setDate(currentDate);
                    slot.setTimeSlot(currentTime);
                    slot.setIsAvailable(true);
                    
                    slots.add(slot);
                    
                    // Move to next time slot
                    currentTime = currentTime.plusMinutes(center.getAppointmentDurationMinutes());
                }
            }
            
            currentDate = currentDate.plusDays(1);
        }
        
        slotRepository.saveAll(slots);
    }
}
