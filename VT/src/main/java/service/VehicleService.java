package service;

import dto.VehicleDTO;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public interface VehicleService {
    
    /**
     * Get driver's vehicles from profile service
     */
    List<String> getDriverVehicleRegistrations(Integer driverId);
    
    /**
     * Get vehicle details by registration
     */
    VehicleDTO getVehicleByRegistration(String registration, Integer driverId);
    
    /**
     * Get all vehicles for a driver (with details)
     */
    List<VehicleDTO> getDriverVehicles(Integer driverId);
}
