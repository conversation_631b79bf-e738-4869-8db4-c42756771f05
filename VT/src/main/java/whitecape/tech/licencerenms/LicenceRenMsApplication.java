package whitecape.tech.licencerenms;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.cloud.netflix.eureka.EnableEurekaClient;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

@SpringBootApplication
@EnableEurekaClient
@EntityScan(basePackages = {"model"})
@EnableJpaRepositories(basePackages = {"repository"})
public class LicenceRenMsApplication {

    public static void main(String[] args) {
        SpringApplication.run(LicenceRenMsApplication.class, args);
        System.out.println("🚗 VT (Vehicle Technical checkup) Service started successfully!");
        System.out.println("📋 Technical Checkup API available at: http://localhost:8083/api/v1/vt");
        System.out.println("🏥 Health Check: http://localhost:8083/actuator/health");
    }
}
