package whitecape.tech.licencerenms;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/test")
public class TestController {

    @GetMapping("/hello")
    public ResponseEntity<Map<String, String>> hello() {
        Map<String, String> response = new HashMap<>();
        response.put("message", "VT Service is running!");
        response.put("timestamp", LocalDateTime.now().toString());
        response.put("service", "vt-service");
        response.put("port", "8083");
        return ResponseEntity.ok(response);
    }

    @GetMapping("/vt-test")
    public ResponseEntity<Map<String, String>> vtTest() {
        Map<String, String> response = new HashMap<>();
        response.put("message", "VT endpoints should be available at /api/v1/vt/*");
        response.put("status", "Component scanning test");
        return ResponseEntity.ok(response);
    }
}
