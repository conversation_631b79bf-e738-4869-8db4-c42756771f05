package dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import model.TechnicalCenter;

import java.time.LocalTime;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class TechnicalCenterDTO {
    private Integer id;
    private String name;
    private String address;
    private String city;
    private String phone;
    private String email;
    private LocalTime openingTime;
    private LocalTime closingTime;
    private Boolean isActive;
    private List<String> workingDays;
    private Integer maxAppointmentsPerDay;
    private Integer appointmentDurationMinutes;

    // Constructor from entity
    public TechnicalCenterDTO(TechnicalCenter center) {
        this.id = center.getId();
        this.name = center.getName();
        this.address = center.getAddress();
        this.city = center.getCity();
        this.phone = center.getPhone();
        this.email = center.getEmail();
        this.openingTime = center.getOpeningTime();
        this.closingTime = center.getClosingTime();
        this.isActive = center.getIsActive();
        this.workingDays = center.getWorkingDays();
        this.maxAppointmentsPerDay = center.getMaxAppointmentsPerDay();
        this.appointmentDurationMinutes = center.getAppointmentDurationMinutes();
    }
}
