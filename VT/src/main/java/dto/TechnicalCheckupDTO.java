package dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import model.TechnicalCheckup;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class TechnicalCheckupDTO {
    private Integer id;
    private Integer driverId;
    private Integer vehicleId;
    private String vehicleRegistration;
    private String vehicleType;
    private String vehicleModel;
    private String vehicleYear;
    private String chassisNumber;
    private String vehicleColor;
    private String vehicleFuelType;
    private Integer centerId;
    private String centerName;
    private LocalDate appointmentDate;
    private String appointmentTime;
    private String status;
    private LocalDateTime createdDate;
    private LocalDateTime updatedDate;
    private String notes;
    private String result;
    private String certificateNumber;
    private LocalDate expiryDate;

    // Constructor from entity
    public TechnicalCheckupDTO(TechnicalCheckup checkup) {
        this.id = checkup.getId();
        this.driverId = checkup.getDriverId();
        this.vehicleId = checkup.getVehicleId();
        this.vehicleRegistration = checkup.getVehicleRegistration();
        this.vehicleType = checkup.getVehicleType();
        this.vehicleModel = checkup.getVehicleModel();
        this.vehicleYear = checkup.getVehicleYear();
        this.chassisNumber = checkup.getChassisNumber();
        this.vehicleColor = checkup.getVehicleColor();
        this.vehicleFuelType = checkup.getVehicleFuelType();
        this.centerId = checkup.getCenterId();
        this.centerName = checkup.getCenterName();
        this.appointmentDate = checkup.getAppointmentDate();
        this.appointmentTime = checkup.getAppointmentTime();
        this.status = checkup.getStatus() != null ? checkup.getStatus().name() : null;
        this.createdDate = checkup.getCreatedDate();
        this.updatedDate = checkup.getUpdatedDate();
        this.notes = checkup.getNotes();
        this.result = checkup.getResult();
        this.certificateNumber = checkup.getCertificateNumber();
        this.expiryDate = checkup.getExpiryDate();
    }
}
