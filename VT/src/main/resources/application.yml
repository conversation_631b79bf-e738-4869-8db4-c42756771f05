server:
  port: 8083

spring:
  application:
    name: vt-service

  datasource:
    url: ********************************************
    username: chirine
    password: chirine
    driver-class-name: org.postgresql.Driver

  jpa:
    hibernate:
      ddl-auto: create
    show-sql: true
    properties:
      hibernate:
        format_sql: true
        dialect: org.hibernate.dialect.PostgreSQLDialect

# Management endpoints
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always

# Logging configuration
logging:
  level:
    org.springframework.web: INFO
    controller: DEBUG
    service: DEBUG
    repository: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# Eureka Configuration
eureka:
  client:
    serviceUrl:
      defaultZone: http://localhost:8761/eureka/
    fetchRegistry: true
    registerWithEureka: true
    healthcheck:
      enabled: true
  instance:
    appName: vt-service
    instanceId: ${spring.application.name}:${spring.application.instance_id:${random.value}}
    preferIpAddress: true
    leaseRenewalIntervalInSeconds: 10
    leaseExpirationDurationInSeconds: 30
    metadata-map:
      version: 1.0.0
      description: "Vehicle Technical checkup Service"
      endpoints: "/api/v1/vt"

# CORS Configuration
cors:
  allowed-origins:
    - http://localhost:3000
    - http://localhost:3001
    - http://127.0.0.1:3000
    - http://127.0.0.1:3001
    - http://localhost:8080
  allowed-methods:
    - GET
    - POST
    - PUT
    - PATCH
    - DELETE
    - OPTIONS
    - HEAD
  allowed-headers: "*"
  allow-credentials: true
  max-age: 3600

# Service-to-service authentication
service:
  auth:
    internal-token: vt-service-internal-token

# VT Service specific configuration
vt:
  service:
    default-appointment-duration: 60 # minutes
    max-appointments-per-day: 20
    advance-booking-days: 30 # how many days in advance can book
    reminder-days-before-expiry: 30 # send reminder X days before expiry

