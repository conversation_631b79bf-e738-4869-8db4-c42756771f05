package Model;

import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;

@Entity
public class Contract {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    private String contractNumber;

    private String startDate;

    private String endDate;

    private String amount;

    private String status;

    private String driverId;

    private String InsuranceCompanyID;
}
