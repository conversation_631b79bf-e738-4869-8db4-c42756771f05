version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:14-alpine
    container_name: taxstick-postgres
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=auth-database
      - POSTGRES_USER=chirine
      - POSTGRES_PASSWORD=chirine
    volumes:
      - postgres-data:/var/lib/postgresql/data
    networks:
      - taxstick-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U chirine -d auth-database"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Client Auth Service
  client-auth:
    build: ./Client-auth
    container_name: taxstick-client-auth
    ports:
      - "8082:8082"
    environment:
      - SPRING_DATASOURCE_URL=*********************************************
      - SPRING_DATASOURCE_USERNAME=chirine
      - SPRING_DATASOURCE_PASSWORD=chirine
      - SPRING_JPA_HIBERNATE_DDL_AUTO=update
      - EMAIL_USERNAME=${EMAIL_USERNAME}
      - EMAIL_PASSWORD=${EMAIL_PASSWORD}
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - taxstick-network
    healthcheck:
      test: ["CMD", "wget", "-q", "-T", "3", "-O", "/dev/null", "http://localhost:8082/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 40s

  # API Gateway
  api-gateway:
    build: ./api-gateway
    container_name: taxstick-api-gateway
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
    depends_on:
      client-auth:
        condition: service_healthy
    networks:
      - taxstick-network
    healthcheck:
      test: ["CMD", "wget", "-q", "-T", "3", "-O", "/dev/null", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

  # Frontend (React)
  frontend:
    build: ./frontend
    container_name: taxstick-frontend
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:8080
    depends_on:
      - api-gateway
    networks:
      - taxstick-network

networks:
  taxstick-network:
    driver: bridge

volumes:
  postgres-data:
