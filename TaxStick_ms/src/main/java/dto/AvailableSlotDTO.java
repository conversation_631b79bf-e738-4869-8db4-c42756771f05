package dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import model.AvailableSlot;

import java.time.LocalDate;
import java.time.LocalTime;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class AvailableSlotDTO {
    private Integer id;
    private Integer centerId;
    private LocalDate date;
    private LocalTime timeSlot;
    private Boolean isAvailable;
    private Integer bookedByDriverId;
    private Integer checkupId;

    // Constructor from entity
    public AvailableSlotDTO(AvailableSlot slot) {
        this.id = slot.getId();
        this.centerId = slot.getCenterId();
        this.date = slot.getDate();
        this.timeSlot = slot.getTimeSlot();
        this.isAvailable = slot.getIsAvailable();
        this.bookedByDriverId = slot.getBookedByDriverId();
        this.checkupId = slot.getCheckupId();
    }
}
