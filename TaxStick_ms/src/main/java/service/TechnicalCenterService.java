package service;

import dto.AvailableSlotDTO;
import dto.TechnicalCenterDTO;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;

@Service
public interface TechnicalCenterService {
    
    /**
     * Get all active technical centers
     */
    List<TechnicalCenterDTO> getAllActiveCenters();
    
    /**
     * Get centers by city
     */
    List<TechnicalCenterDTO> getCentersByCity(String city);
    
    /**
     * Get center by ID
     */
    TechnicalCenterDTO getCenterById(Integer centerId);
    
    /**
     * Get available dates for a center
     */
    List<AvailableSlotDTO> getAvailableDates(Integer centerId);
    
    /**
     * Get available slots for a center on a specific date
     */
    List<AvailableSlotDTO> getAvailableSlots(Integer centerId, LocalDate date);
    
    /**
     * Book a slot
     */
    AvailableSlotDTO bookSlot(Integer slotId, Integer driverId, Integer checkupId);
    
    /**
     * Generate available slots for a center (admin function)
     */
    void generateAvailableSlots(Integer centerId, LocalDate startDate, LocalDate endDate);
}
