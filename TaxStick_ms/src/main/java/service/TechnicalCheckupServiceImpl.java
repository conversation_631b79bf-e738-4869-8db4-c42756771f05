package service;

import dto.CreateCheckupRequest;
import dto.TechnicalCheckupDTO;
import model.TechnicalCheckup;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import repository.TechnicalCheckupRepository;
import repository.TechnicalCenterRepository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Transactional
public class TechnicalCheckupServiceImpl implements TechnicalCheckupService {

    @Autowired
    private TechnicalCheckupRepository checkupRepository;
    
    @Autowired
    private TechnicalCenterRepository centerRepository;

    @Override
    public TechnicalCheckupDTO createCheckup(CreateCheckupRequest request, Integer driverId) {
        TechnicalCheckup checkup = new TechnicalCheckup();
        checkup.setDriverId(driverId);
        checkup.setVehicleRegistration(request.getVehicleRegistration());
        checkup.setVehicleType(request.getVehicleType());
        checkup.setVehicleModel(request.getVehicleModel());
        checkup.setVehicleYear(request.getVehicleYear());
        checkup.setChassisNumber(request.getChassisNumber());
        checkup.setVehicleColor(request.getVehicleColor());
        checkup.setVehicleFuelType(request.getVehicleFuelType());
        checkup.setNotes(request.getNotes());
        checkup.setStatus(TechnicalCheckup.CheckupStatus.PENDING);
        checkup.setCreatedDate(LocalDateTime.now());
        checkup.setUpdatedDate(LocalDateTime.now());

        TechnicalCheckup savedCheckup = checkupRepository.save(checkup);
        return new TechnicalCheckupDTO(savedCheckup);
    }

    @Override
    @Transactional(readOnly = true)
    public List<TechnicalCheckupDTO> getDriverCheckups(Integer driverId) {
        List<TechnicalCheckup> checkups = checkupRepository.findByDriverId(driverId);
        return checkups.stream()
                .map(TechnicalCheckupDTO::new)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public TechnicalCheckupDTO getCheckupById(Integer checkupId) {
        TechnicalCheckup checkup = checkupRepository.findById(checkupId)
                .orElseThrow(() -> new RuntimeException("Technical checkup not found with id: " + checkupId));
        return new TechnicalCheckupDTO(checkup);
    }

    @Override
    public TechnicalCheckupDTO updateCheckupStatus(Integer checkupId, TechnicalCheckup.CheckupStatus status) {
        TechnicalCheckup checkup = checkupRepository.findById(checkupId)
                .orElseThrow(() -> new RuntimeException("Technical checkup not found with id: " + checkupId));
        
        checkup.setStatus(status);
        checkup.setUpdatedDate(LocalDateTime.now());
        
        TechnicalCheckup updatedCheckup = checkupRepository.save(checkup);
        return new TechnicalCheckupDTO(updatedCheckup);
    }

    @Override
    public TechnicalCheckupDTO scheduleAppointment(Integer checkupId, Integer centerId, String appointmentDate, String appointmentTime) {
        TechnicalCheckup checkup = checkupRepository.findById(checkupId)
                .orElseThrow(() -> new RuntimeException("Technical checkup not found with id: " + checkupId));
        
        // Get center information
        var center = centerRepository.findById(centerId)
                .orElseThrow(() -> new RuntimeException("Technical center not found with id: " + centerId));
        
        checkup.setCenterId(centerId);
        checkup.setCenterName(center.getName());
        checkup.setAppointmentDate(LocalDate.parse(appointmentDate));
        checkup.setAppointmentTime(appointmentTime);
        checkup.setStatus(TechnicalCheckup.CheckupStatus.SCHEDULED);
        checkup.setUpdatedDate(LocalDateTime.now());
        
        TechnicalCheckup updatedCheckup = checkupRepository.save(checkup);
        return new TechnicalCheckupDTO(updatedCheckup);
    }

    @Override
    public void cancelCheckup(Integer checkupId, Integer driverId) {
        TechnicalCheckup checkup = checkupRepository.findById(checkupId)
                .orElseThrow(() -> new RuntimeException("Technical checkup not found with id: " + checkupId));
        
        // Verify the checkup belongs to the driver
        if (!checkup.getDriverId().equals(driverId)) {
            throw new RuntimeException("You can only cancel your own checkups");
        }
        
        checkup.setStatus(TechnicalCheckup.CheckupStatus.CANCELLED);
        checkup.setUpdatedDate(LocalDateTime.now());
        
        checkupRepository.save(checkup);
    }

    @Override
    @Transactional(readOnly = true)
    public List<TechnicalCheckupDTO> getDriverCheckupHistory(Integer driverId) {
        List<TechnicalCheckup> checkups = checkupRepository.findByDriverIdOrderByCreatedDateDesc(driverId);
        return checkups.stream()
                .map(TechnicalCheckupDTO::new)
                .collect(Collectors.toList());
    }

    @Override
    public TechnicalCheckupDTO completeCheckup(Integer checkupId, String result, String certificateNumber) {
        TechnicalCheckup checkup = checkupRepository.findById(checkupId)
                .orElseThrow(() -> new RuntimeException("Technical checkup not found with id: " + checkupId));
        
        checkup.setStatus(TechnicalCheckup.CheckupStatus.COMPLETED);
        checkup.setResult(result);
        checkup.setCertificateNumber(certificateNumber);
        
        // Set expiry date (typically 1 year from completion)
        if ("PASSED".equals(result)) {
            checkup.setExpiryDate(LocalDate.now().plusYears(1));
        }
        
        checkup.setUpdatedDate(LocalDateTime.now());
        
        TechnicalCheckup updatedCheckup = checkupRepository.save(checkup);
        return new TechnicalCheckupDTO(updatedCheckup);
    }
}
