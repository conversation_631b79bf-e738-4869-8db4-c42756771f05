package model;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalTime;
import java.util.List;

@Entity
@Table(name = "technical_centers")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class TechnicalCenter {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "name", nullable = false)
    private String name;

    @Column(name = "address")
    private String address;

    @Column(name = "city")
    private String city;

    @Column(name = "phone")
    private String phone;

    @Column(name = "email")
    private String email;

    @Column(name = "opening_time")
    private LocalTime openingTime;

    @Column(name = "closing_time")
    private LocalTime closingTime;

    @Column(name = "is_active")
    private Boolean isActive = true;

    @ElementCollection
    @CollectionTable(name = "center_working_days", joinColumns = @JoinColumn(name = "center_id"))
    @Column(name = "day_of_week")
    private List<String> workingDays; // MONDAY, TUESDAY, etc.

    @Column(name = "max_appointments_per_day")
    private Integer maxAppointmentsPerDay = 20;

    @Column(name = "appointment_duration_minutes")
    private Integer appointmentDurationMinutes = 60;
}
