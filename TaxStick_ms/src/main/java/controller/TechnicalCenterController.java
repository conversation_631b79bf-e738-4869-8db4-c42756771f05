package controller;

import dto.AvailableSlotDTO;
import dto.TechnicalCenterDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import service.TechnicalCenterService;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/v1/vt/centers")
@CrossOrigin(origins = {"http://localhost:3000", "http://localhost:3001"})
public class TechnicalCenterController {

    @Autowired
    private TechnicalCenterService centerService;

    /**
     * Get all active technical centers
     */
    @GetMapping
    public ResponseEntity<List<TechnicalCenterDTO>> getAllCenters() {
        List<TechnicalCenterDTO> centers = centerService.getAllActiveCenters();
        return ResponseEntity.ok(centers);
    }

    /**
     * Get centers by city
     */
    @GetMapping("/city/{city}")
    public ResponseEntity<List<TechnicalCenterDTO>> getCentersByCity(@PathVariable String city) {
        List<TechnicalCenterDTO> centers = centerService.getCentersByCity(city);
        return ResponseEntity.ok(centers);
    }

    /**
     * Get center by ID
     */
    @GetMapping("/{centerId}")
    public ResponseEntity<TechnicalCenterDTO> getCenterById(@PathVariable Integer centerId) {
        try {
            TechnicalCenterDTO center = centerService.getCenterById(centerId);
            return ResponseEntity.ok(center);
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * Get available dates for a center
     */
    @GetMapping("/{centerId}/available-dates")
    public ResponseEntity<List<AvailableSlotDTO>> getAvailableDates(@PathVariable Integer centerId) {
        try {
            List<AvailableSlotDTO> availableDates = centerService.getAvailableDates(centerId);
            return ResponseEntity.ok(availableDates);
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * Get available slots for a center on a specific date
     */
    @GetMapping("/{centerId}/available-slots")
    public ResponseEntity<List<AvailableSlotDTO>> getAvailableSlots(
            @PathVariable Integer centerId,
            @RequestParam String date) {
        
        try {
            LocalDate localDate = LocalDate.parse(date);
            List<AvailableSlotDTO> availableSlots = centerService.getAvailableSlots(centerId, localDate);
            return ResponseEntity.ok(availableSlots);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Book a slot
     */
    @PostMapping("/slots/{slotId}/book")
    public ResponseEntity<AvailableSlotDTO> bookSlot(
            @PathVariable Integer slotId,
            @RequestBody Map<String, Integer> bookingRequest,
            @RequestHeader("X-User-Email") String userEmail) {
        
        try {
            Integer driverId = extractDriverIdFromEmail(userEmail);
            Integer checkupId = bookingRequest.get("checkupId");
            
            AvailableSlotDTO bookedSlot = centerService.bookSlot(slotId, driverId, checkupId);
            return ResponseEntity.ok(bookedSlot);
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Generate available slots for a center (admin function)
     */
    @PostMapping("/{centerId}/generate-slots")
    public ResponseEntity<Map<String, String>> generateSlots(
            @PathVariable Integer centerId,
            @RequestBody Map<String, String> request) {
        
        try {
            LocalDate startDate = LocalDate.parse(request.get("startDate"));
            LocalDate endDate = LocalDate.parse(request.get("endDate"));
            
            centerService.generateAvailableSlots(centerId, startDate, endDate);
            
            Map<String, String> response = new HashMap<>();
            response.put("message", "Available slots generated successfully");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, String> response = new HashMap<>();
            response.put("error", "Failed to generate slots: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    // Helper method to extract driver ID from email
    private Integer extractDriverIdFromEmail(String email) {
        // Mock implementation - return a fixed driver ID for testing
        return 1;
    }
}
