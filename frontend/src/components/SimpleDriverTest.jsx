import React, { useState } from 'react';
import { getDriversSimple } from '../Services/simpleDriverService';

const SimpleDriverTest = () => {
  const [drivers, setDrivers] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const testGetDrivers = async () => {
    setLoading(true);
    setError(null);
    setDrivers(null);

    try {
      console.log('🧪 Testing getDrivers endpoint (no auth)...');
      const data = await getDriversSimple();
      setDrivers(data);
    } catch (err) {
      console.error('❌ Test failed:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <h2>Simple Driver API Test (No Authentication)</h2>
      
      <div style={{ marginBottom: '20px' }}>
        <button 
          onClick={testGetDrivers}
          disabled={loading}
          style={{ 
            padding: '15px 30px',
            backgroundColor: '#4CAF50',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: loading ? 'not-allowed' : 'pointer',
            fontSize: '16px'
          }}
        >
          {loading ? 'Testing...' : 'Test Get All Drivers'}
        </button>
      </div>

      {/* Error Display */}
      {error && (
        <div style={{ 
          color: 'red', 
          backgroundColor: '#ffebee', 
          padding: '15px', 
          borderRadius: '4px',
          marginBottom: '20px',
          border: '1px solid #f44336'
        }}>
          <h3>❌ Error</h3>
          <p>{error}</p>
        </div>
      )}

      {/* Success Display */}
      {drivers && (
        <div style={{ 
          backgroundColor: '#e8f5e8', 
          padding: '15px', 
          borderRadius: '4px',
          marginBottom: '20px',
          border: '1px solid #4CAF50'
        }}>
          <h3>✅ Success - Found {Array.isArray(drivers) ? drivers.length : 0} drivers</h3>
          <pre style={{ 
            backgroundColor: '#fff', 
            padding: '10px', 
            borderRadius: '4px',
            overflow: 'auto',
            maxHeight: '400px'
          }}>
            {JSON.stringify(drivers, null, 2)}
          </pre>
        </div>
      )}

      {/* Instructions */}
      <div style={{ 
        backgroundColor: '#f5f5f5', 
        padding: '15px', 
        borderRadius: '4px',
        marginTop: '20px'
      }}>
        <h3>Endpoint Details</h3>
        <p><strong>URL:</strong> http://localhost:8082/api/v1/driverprofile/getDrivers</p>
        <p><strong>Method:</strong> GET</p>
        <p><strong>Authentication:</strong> None (for testing)</p>
        
        <h4>Expected Results:</h4>
        <ul>
          <li>If successful: Array of driver objects</li>
          <li>If no drivers exist: Empty array []</li>
          <li>If endpoint not found: 404 error</li>
          <li>If security blocks it: 403 error</li>
        </ul>
      </div>
    </div>
  );
};

export default SimpleDriverTest;
