import React, { useState } from 'react';
import { getDrivers } from '../Services/driverService';
import { isAuthenticated, getCurrentUser } from '../Services/authservice';

const DriverTest = () => {
  const [drivers, setDrivers] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const currentUser = getCurrentUser();
  const authenticated = isAuthenticated();

  const testGetDrivers = async () => {
    setLoading(true);
    setError(null);
    setDrivers(null);

    try {
      console.log('🧪 Testing getDrivers endpoint...');
      
      // Check authentication first
      const token = localStorage.getItem('token');
      console.log('🔑 Token available:', !!token);
      console.log('🔑 Token preview:', token ? token.substring(0, 20) + '...' : 'None');
      
      if (!token) {
        throw new Error('No authentication token found. Please log in first.');
      }

      const data = await getDrivers();
      setDrivers(data);
    } catch (err) {
      console.error('❌ Test failed:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const testDirectAPI = async () => {
    setLoading(true);
    setError(null);
    setDrivers(null);

    try {
      console.log('🧪 Testing direct API call...');
      
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('No authentication token found. Please log in first.');
      }

      const response = await fetch('http://localhost:8082/getDrivers', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      });

      console.log('📡 Response status:', response.status);
      console.log('📡 Response headers:', Object.fromEntries(response.headers.entries()));

      if (!response.ok) {
        const errorText = await response.text();
        console.log('❌ Error response:', errorText);
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      const data = await response.json();
      console.log('✅ Success response:', data);
      setDrivers(data);
    } catch (err) {
      console.error('❌ Direct API test failed:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <h2>Driver API Test</h2>
      
      {/* Authentication Status */}
      <div style={{ 
        padding: '15px', 
        marginBottom: '20px', 
        backgroundColor: authenticated ? '#e8f5e8' : '#ffebee',
        borderRadius: '4px',
        border: `1px solid ${authenticated ? '#4CAF50' : '#f44336'}`
      }}>
        <h3>Authentication Status</h3>
        <p><strong>Authenticated:</strong> {authenticated ? '✅ Yes' : '❌ No'}</p>
        {currentUser && (
          <>
            <p><strong>Username:</strong> {currentUser.username}</p>
            <p><strong>Role:</strong> {currentUser.role}</p>
            <p><strong>Authorities:</strong> {currentUser.authorities?.join(', ')}</p>
          </>
        )}
      </div>

      {/* Test Buttons */}
      <div style={{ marginBottom: '20px' }}>
        <button 
          onClick={testGetDrivers}
          disabled={loading}
          style={{ 
            marginRight: '10px', 
            padding: '10px 20px',
            backgroundColor: '#2196F3',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: loading ? 'not-allowed' : 'pointer'
          }}
        >
          {loading ? 'Testing...' : 'Test getDrivers (via Service)'}
        </button>
        
        <button 
          onClick={testDirectAPI}
          disabled={loading}
          style={{ 
            padding: '10px 20px',
            backgroundColor: '#FF9800',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: loading ? 'not-allowed' : 'pointer'
          }}
        >
          {loading ? 'Testing...' : 'Test Direct API Call'}
        </button>
      </div>

      {/* Error Display */}
      {error && (
        <div style={{ 
          color: 'red', 
          backgroundColor: '#ffebee', 
          padding: '15px', 
          borderRadius: '4px',
          marginBottom: '20px',
          border: '1px solid #f44336'
        }}>
          <h3>❌ Error</h3>
          <p>{error}</p>
        </div>
      )}

      {/* Success Display */}
      {drivers && (
        <div style={{ 
          backgroundColor: '#e8f5e8', 
          padding: '15px', 
          borderRadius: '4px',
          marginBottom: '20px',
          border: '1px solid #4CAF50'
        }}>
          <h3>✅ Success</h3>
          <pre style={{ 
            backgroundColor: '#fff', 
            padding: '10px', 
            borderRadius: '4px',
            overflow: 'auto',
            maxHeight: '300px'
          }}>
            {JSON.stringify(drivers, null, 2)}
          </pre>
        </div>
      )}

      {/* Instructions */}
      <div style={{ 
        backgroundColor: '#f5f5f5', 
        padding: '15px', 
        borderRadius: '4px',
        marginTop: '20px'
      }}>
        <h3>Instructions</h3>
        <ol>
          <li>Make sure you're logged in first (check authentication status above)</li>
          <li>Try "Test getDrivers (via Service)" first</li>
          <li>If that fails, try "Test Direct API Call" to see the raw response</li>
          <li>Check the browser console for detailed logs</li>
          <li>If you get a 403 error, check your Spring Security configuration</li>
        </ol>
        
        <h4>Common Issues:</h4>
        <ul>
          <li><strong>401 Unauthorized:</strong> Token is missing or expired</li>
          <li><strong>403 Forbidden:</strong> User doesn't have the required role/permission</li>
          <li><strong>404 Not Found:</strong> Endpoint doesn't exist</li>
          <li><strong>CORS Error:</strong> CORS configuration issue</li>
        </ul>
      </div>
    </div>
  );
};

export default DriverTest;
