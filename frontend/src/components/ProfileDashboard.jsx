import React, { useState, useEffect } from 'react';
import { useProfile, useVehicles } from '../hooks/useProfile';
import Navigation from './Navigation';
import './ProfileDashboard.css';

const ProfileDashboard = () => {
  const {
    profile,
    loading: profileLoading,
    error: profileError,
    fetchProfile,
    updateUsername,
    updatePersonalInfo,
    updateEmail,
    changePassword,
    clearError: clearProfileError
  } = useProfile();

  const {
    vehicles,
    loading: vehiclesLoading,
    error: vehiclesError,
    fetchVehicles,
    addVehicle,
    removeVehicle,
    clearError: clearVehiclesError
  } = useVehicles();

  const [activeTab, setActiveTab] = useState('overview');
  const [showModal, setShowModal] = useState(false);
  const [modalType, setModalType] = useState('');
  const [formData, setFormData] = useState({
    username: '',
    firstname: '',
    lastname: '',
    email: '',
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
    vehicleRegistration: ''
  });
  const [successMessage, setSuccessMessage] = useState('');

  useEffect(() => {
    fetchProfile();
    fetchVehicles();
  }, []);

  useEffect(() => {
    if (profile) {
      setFormData(prev => ({
        ...prev,
        username: profile.username || '',
        firstname: profile.firstname || '',
        lastname: profile.lastname || '',
        email: profile.email || ''
      }));
    }
  }, [profile]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const openModal = (type) => {
    setModalType(type);
    setShowModal(true);
    clearProfileError();
    clearVehiclesError();
    setSuccessMessage('');
  };

  const closeModal = () => {
    setShowModal(false);
    setModalType('');
    setFormData(prev => ({
      ...prev,
      currentPassword: '',
      newPassword: '',
      confirmPassword: '',
      vehicleRegistration: ''
    }));
  };

  const showSuccess = (message) => {
    setSuccessMessage(message);
    setTimeout(() => setSuccessMessage(''), 5000);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      switch (modalType) {
        case 'username':
          await updateUsername(formData.username, formData.currentPassword);
          showSuccess('Username updated successfully!');
          break;
        case 'personalInfo':
          await updatePersonalInfo({
            firstname: formData.firstname,
            lastname: formData.lastname,
            dateOfBirth: profile.dateOfBirth
          });
          showSuccess('Personal information updated successfully!');
          break;
        case 'email':
          await updateEmail(formData.email, formData.currentPassword);
          showSuccess('Email updated successfully! Please check your email for verification.');
          break;
        case 'password':
          await changePassword(formData.currentPassword, formData.newPassword, formData.confirmPassword);
          showSuccess('Password changed successfully!');
          break;
        case 'vehicle':
          await addVehicle(formData.vehicleRegistration);
          showSuccess('Vehicle added successfully!');
          break;
        default:
          break;
      }
      closeModal();
    } catch (error) {
      // Error is handled by the hook
    }
  };

  const handleRemoveVehicle = async (vehicleReg) => {
    if (window.confirm(`Are you sure you want to remove vehicle ${vehicleReg}?`)) {
      try {
        await removeVehicle(vehicleReg);
        showSuccess('Vehicle removed successfully!');
      } catch (error) {
        // Error is handled by the hook
      }
    }
  };

  if (profileLoading && !profile) {
    return (
      <div className="profile-loading">
        <div className="loading-spinner"></div>
        <p>Loading your profile...</p>
      </div>
    );
  }

  return (
    <>
      <Navigation />
      <div className="profile-dashboard">
      {/* Header */}
      <div className="profile-header">
        <div className="profile-avatar">
          <div className="avatar-circle">
            {profile?.firstname?.charAt(0)}{profile?.lastname?.charAt(0)}
          </div>
        </div>
        <div className="profile-info">
          <h1>{profile?.firstname} {profile?.lastname}</h1>
          <p className="profile-email">{profile?.email}</p>
          <div className="profile-badges">
            <span className={`badge ${profile?.emailVerified ? 'verified' : 'unverified'}`}>
              {profile?.emailVerified ? '✓ Email Verified' : '⚠ Email Not Verified'}
            </span>
            <span className="badge role">Driver</span>
          </div>
        </div>
      </div>

      {/* Success Message */}
      {successMessage && (
        <div className="success-message">
          <span>✓ {successMessage}</span>
          <button onClick={() => setSuccessMessage('')}>×</button>
        </div>
      )}

      {/* Error Message */}
      {(profileError || vehiclesError) && (
        <div className="error-message">
          <span>⚠ {profileError || vehiclesError}</span>
          <button onClick={() => { clearProfileError(); clearVehiclesError(); }}>×</button>
        </div>
      )}

      {/* Navigation Tabs */}
      <div className="profile-tabs">
        {[
          { id: 'overview', label: 'Overview', icon: '👤' },
          { id: 'security', label: 'Security', icon: '🔒' },
          { id: 'vehicles', label: 'Vehicles', icon: '🚗' }
        ].map(tab => (
          <button
            key={tab.id}
            className={`tab ${activeTab === tab.id ? 'active' : ''}`}
            onClick={() => setActiveTab(tab.id)}
          >
            <span className="tab-icon">{tab.icon}</span>
            {tab.label}
          </button>
        ))}
      </div>

      {/* Tab Content */}
      <div className="tab-content">
        {/* Overview Tab */}
        {activeTab === 'overview' && (
          <div className="overview-tab">
            <div className="profile-cards">
              <div className="profile-card">
                <div className="card-header">
                  <h3>Personal Information</h3>
                  <button className="edit-btn" onClick={() => openModal('personalInfo')}>
                    ✏️ Edit
                  </button>
                </div>
                <div className="card-content">
                  <div className="info-row">
                    <span className="label">First Name:</span>
                    <span className="value">{profile?.firstname || 'Not set'}</span>
                  </div>
                  <div className="info-row">
                    <span className="label">Last Name:</span>
                    <span className="value">{profile?.lastname || 'Not set'}</span>
                  </div>
                  <div className="info-row">
                    <span className="label">Date of Birth:</span>
                    <span className="value">
                      {profile?.dateOfBirth ? new Date(profile.dateOfBirth).toLocaleDateString() : 'Not set'}
                    </span>
                  </div>
                </div>
              </div>

              <div className="profile-card">
                <div className="card-header">
                  <h3>Account Details</h3>
                  <button className="edit-btn" onClick={() => openModal('username')}>
                    ✏️ Edit
                  </button>
                </div>
                <div className="card-content">
                  <div className="info-row">
                    <span className="label">Username:</span>
                    <span className="value">{profile?.username || 'Not set'}</span>
                  </div>
                  <div className="info-row">
                    <span className="label">Email:</span>
                    <span className="value">{profile?.email || 'Not set'}</span>
                    <button className="link-btn" onClick={() => openModal('email')}>
                      Change Email
                    </button>
                  </div>
                  <div className="info-row">
                    <span className="label">Member Since:</span>
                    <span className="value">
                      {profile?.createdDate ? new Date(profile.createdDate).toLocaleDateString() : 'Unknown'}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Security Tab */}
        {activeTab === 'security' && (
          <div className="security-tab">
            <div className="profile-card">
              <div className="card-header">
                <h3>Password & Security</h3>
              </div>
              <div className="card-content">
                <div className="security-item">
                  <div className="security-info">
                    <h4>Password</h4>
                    <p>Last changed: Unknown</p>
                  </div>
                  <button className="btn btn-primary" onClick={() => openModal('password')}>
                    Change Password
                  </button>
                </div>
                <div className="security-item">
                  <div className="security-info">
                    <h4>Email Verification</h4>
                    <p>Status: {profile?.emailVerified ? 'Verified' : 'Not Verified'}</p>
                  </div>
                  {!profile?.emailVerified && (
                    <button className="btn btn-secondary">
                      Resend Verification
                    </button>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Vehicles Tab */}
        {activeTab === 'vehicles' && (
          <div className="vehicles-tab">
            <div className="profile-card">
              <div className="card-header">
                <h3>Your Vehicles</h3>
                <button className="btn btn-primary" onClick={() => openModal('vehicle')}>
                  + Add Vehicle
                </button>
              </div>
              <div className="card-content">
                {vehiclesLoading ? (
                  <div className="loading-text">Loading vehicles...</div>
                ) : vehicles.length > 0 ? (
                  <div className="vehicles-list">
                    {vehicles.map((vehicle, index) => (
                      <div key={index} className="vehicle-item">
                        <div className="vehicle-info">
                          <span className="vehicle-icon">🚗</span>
                          <span className="vehicle-reg">{vehicle}</span>
                        </div>
                        <button
                          className="btn btn-danger btn-sm"
                          onClick={() => handleRemoveVehicle(vehicle)}
                        >
                          Remove
                        </button>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="empty-state">
                    <span className="empty-icon">🚗</span>
                    <p>No vehicles registered</p>
                    <button className="btn btn-primary" onClick={() => openModal('vehicle')}>
                      Add Your First Vehicle
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Modal */}
      {showModal && (
        <div className="modal-overlay" onClick={closeModal}>
          <div className="modal" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h3>
                {modalType === 'username' && 'Update Username'}
                {modalType === 'personalInfo' && 'Update Personal Information'}
                {modalType === 'email' && 'Update Email Address'}
                {modalType === 'password' && 'Change Password'}
                {modalType === 'vehicle' && 'Add Vehicle Registration'}
              </h3>
              <button className="close-btn" onClick={closeModal}>×</button>
            </div>

            <form onSubmit={handleSubmit} className="modal-form">
              {modalType === 'username' && (
                <>
                  <div className="form-group">
                    <label>New Username</label>
                    <input
                      type="text"
                      name="username"
                      value={formData.username}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                  <div className="form-group">
                    <label>Current Password</label>
                    <input
                      type="password"
                      name="currentPassword"
                      value={formData.currentPassword}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                </>
              )}

              {modalType === 'personalInfo' && (
                <>
                  <div className="form-group">
                    <label>First Name</label>
                    <input
                      type="text"
                      name="firstname"
                      value={formData.firstname}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                  <div className="form-group">
                    <label>Last Name</label>
                    <input
                      type="text"
                      name="lastname"
                      value={formData.lastname}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                </>
              )}

              {modalType === 'email' && (
                <>
                  <div className="form-group">
                    <label>New Email Address</label>
                    <input
                      type="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                  <div className="form-group">
                    <label>Current Password</label>
                    <input
                      type="password"
                      name="currentPassword"
                      value={formData.currentPassword}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                </>
              )}

              {modalType === 'password' && (
                <>
                  <div className="form-group">
                    <label>Current Password</label>
                    <input
                      type="password"
                      name="currentPassword"
                      value={formData.currentPassword}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                  <div className="form-group">
                    <label>New Password</label>
                    <input
                      type="password"
                      name="newPassword"
                      value={formData.newPassword}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                  <div className="form-group">
                    <label>Confirm New Password</label>
                    <input
                      type="password"
                      name="confirmPassword"
                      value={formData.confirmPassword}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                </>
              )}

              {modalType === 'vehicle' && (
                <div className="form-group">
                  <label>Vehicle Registration Number</label>
                  <input
                    type="text"
                    name="vehicleRegistration"
                    value={formData.vehicleRegistration}
                    onChange={handleInputChange}
                    placeholder="e.g., ABC123"
                    required
                  />
                </div>
              )}

              <div className="modal-actions">
                <button type="button" className="btn btn-secondary" onClick={closeModal}>
                  Cancel
                </button>
                <button
                  type="submit"
                  className="btn btn-primary"
                  disabled={profileLoading || vehiclesLoading}
                >
                  {(profileLoading || vehiclesLoading) ? 'Saving...' : 'Save Changes'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
      </div>
    </>
  );
};

export default ProfileDashboard;
