import React, { useState } from 'react';
import { login } from '../services/authService';

const LoginDebugger = () => {
  const [credentials, setCredentials] = useState({
    email: '',
    password: ''
  });
  const [result, setResult] = useState(null);
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(false);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setCredentials(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const clearStorage = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    console.log('Storage cleared');
    setResult(null);
    setError(null);
  };

  const checkStorage = () => {
    const token = localStorage.getItem('token');
    const user = localStorage.getItem('user');
    console.log('Token:', token);
    console.log('User:', user);
    alert(`Token: ${token ? 'Found' : 'Not found'}\nUser: ${user ? 'Found' : 'Not found'}`);
  };

  const testLogin = async () => {
    setLoading(true);
    setResult(null);
    setError(null);
    
    try {
      console.log('Testing login with:', credentials);
      
      // Clear storage first
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      
      const response = await login(credentials);
      console.log('Login response:', response);
      setResult(response);
      
      // Check if token was stored
      const storedToken = localStorage.getItem('token');
      console.log('Token stored:', storedToken ? 'Yes' : 'No');
      
    } catch (err) {
      console.error('Login error:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const testDirectAPI = async () => {
    setLoading(true);
    setResult(null);
    setError(null);
    
    try {
      console.log('Testing direct API call...');
      
      const response = await fetch('http://localhost:8082/api/v1/auth/authenticate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(credentials),
      });
      
      console.log('Response status:', response.status);
      console.log('Response headers:', [...response.headers.entries()]);
      
      if (!response.ok) {
        const errorText = await response.text();
        console.log('Error response:', errorText);
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }
      
      const data = await response.json();
      console.log('Direct API response:', data);
      setResult(data);
      
    } catch (err) {
      console.error('Direct API error:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ padding: '20px', maxWidth: '600px', margin: '0 auto' }}>
      <h2>Login Debugger</h2>
      
      <div style={{ marginBottom: '20px' }}>
        <div style={{ marginBottom: '10px' }}>
          <label>Email:</label>
          <input 
            type="email" 
            name="email" 
            value={credentials.email} 
            onChange={handleChange}
            style={{ marginLeft: '10px', padding: '5px', width: '200px' }}
          />
        </div>
        <div style={{ marginBottom: '10px' }}>
          <label>Password:</label>
          <input 
            type="password" 
            name="password" 
            value={credentials.password} 
            onChange={handleChange}
            style={{ marginLeft: '10px', padding: '5px', width: '200px' }}
          />
        </div>
      </div>
      
      <div style={{ marginBottom: '20px' }}>
        <button 
          onClick={testLogin} 
          disabled={loading}
          style={{ marginRight: '10px', padding: '10px' }}
        >
          {loading ? 'Testing...' : 'Test Login (via authService)'}
        </button>
        
        <button 
          onClick={testDirectAPI} 
          disabled={loading}
          style={{ marginRight: '10px', padding: '10px' }}
        >
          {loading ? 'Testing...' : 'Test Direct API Call'}
        </button>
        
        <button 
          onClick={clearStorage}
          style={{ marginRight: '10px', padding: '10px' }}
        >
          Clear Storage
        </button>
        
        <button 
          onClick={checkStorage}
          style={{ padding: '10px' }}
        >
          Check Storage
        </button>
      </div>
      
      {error && (
        <div style={{ 
          color: 'red', 
          backgroundColor: '#ffebee', 
          padding: '10px', 
          borderRadius: '4px',
          marginBottom: '20px'
        }}>
          <strong>Error:</strong> {error}
        </div>
      )}
      
      {result && (
        <div style={{ 
          backgroundColor: '#e8f5e8', 
          padding: '10px', 
          borderRadius: '4px',
          marginBottom: '20px'
        }}>
          <strong>Success:</strong>
          <pre style={{ marginTop: '10px' }}>
            {JSON.stringify(result, null, 2)}
          </pre>
        </div>
      )}
      
      <div style={{ marginTop: '20px' }}>
        <h3>Instructions:</h3>
        <ol>
          <li>Enter your credentials</li>
          <li>Try "Test Login" first to see if the authService works</li>
          <li>If that fails, try "Test Direct API Call" to bypass the service</li>
          <li>Check the browser console for detailed logs</li>
          <li>Use "Clear Storage" to clean up between tests</li>
        </ol>
      </div>
    </div>
  );
};

export default LoginDebugger;
