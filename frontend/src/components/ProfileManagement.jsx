import React, { useState, useEffect } from 'react';
import { useProfile, useVehicles } from '../hooks/useProfile';

const ProfileManagement = () => {
  const {
    profile,
    loading: profileLoading,
    error: profileError,
    fetchProfile,
    updateUsername,
    updatePersonalInfo,
    updateEmail,
    changePassword,
    clearError: clearProfileError
  } = useProfile();

  const {
    vehicles,
    loading: vehiclesLoading,
    error: vehiclesError,
    fetchVehicles,
    addVehicle,
    removeVehicle,
    clearError: clearVehiclesError
  } = useVehicles();

  const [activeTab, setActiveTab] = useState('profile');
  const [formData, setFormData] = useState({
    username: '',
    firstname: '',
    lastname: '',
    email: '',
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
    vehicleRegistration: ''
  });

  useEffect(() => {
    fetchProfile();
    fetchVehicles();
  }, []);

  useEffect(() => {
    if (profile) {
      setFormData(prev => ({
        ...prev,
        username: profile.username || '',
        firstname: profile.firstname || '',
        lastname: profile.lastname || '',
        email: profile.email || ''
      }));
    }
  }, [profile]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleUpdateUsername = async (e) => {
    e.preventDefault();
    try {
      await updateUsername(formData.username, formData.currentPassword);
      alert('Username updated successfully!');
      setFormData(prev => ({ ...prev, currentPassword: '' }));
    } catch (error) {
      alert(`Error: ${error.message}`);
    }
  };

  const handleUpdatePersonalInfo = async (e) => {
    e.preventDefault();
    try {
      await updatePersonalInfo({
        firstname: formData.firstname,
        lastname: formData.lastname,
        dateOfBirth: profile.dateOfBirth // Keep existing date of birth
      });
      alert('Personal information updated successfully!');
    } catch (error) {
      alert(`Error: ${error.message}`);
    }
  };

  const handleUpdateEmail = async (e) => {
    e.preventDefault();
    try {
      await updateEmail(formData.email, formData.currentPassword);
      alert('Email updated successfully! Please check your email for verification.');
      setFormData(prev => ({ ...prev, currentPassword: '' }));
    } catch (error) {
      alert(`Error: ${error.message}`);
    }
  };

  const handleChangePassword = async (e) => {
    e.preventDefault();
    try {
      await changePassword(formData.currentPassword, formData.newPassword, formData.confirmPassword);
      alert('Password changed successfully!');
      setFormData(prev => ({
        ...prev,
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      }));
    } catch (error) {
      alert(`Error: ${error.message}`);
    }
  };

  const handleAddVehicle = async (e) => {
    e.preventDefault();
    try {
      await addVehicle(formData.vehicleRegistration);
      alert('Vehicle added successfully!');
      setFormData(prev => ({ ...prev, vehicleRegistration: '' }));
    } catch (error) {
      alert(`Error: ${error.message}`);
    }
  };

  const handleRemoveVehicle = async (vehicleReg) => {
    if (window.confirm(`Are you sure you want to remove vehicle ${vehicleReg}?`)) {
      try {
        await removeVehicle(vehicleReg);
        alert('Vehicle removed successfully!');
      } catch (error) {
        alert(`Error: ${error.message}`);
      }
    }
  };

  if (profileLoading && !profile) {
    return <div>Loading profile...</div>;
  }

  return (
    <div style={{ maxWidth: '800px', margin: '0 auto', padding: '20px' }}>
      <h1>Profile Management</h1>

      {/* Tab Navigation */}
      <div style={{ marginBottom: '20px', borderBottom: '1px solid #ccc' }}>
        {['profile', 'security', 'vehicles'].map(tab => (
          <button
            key={tab}
            onClick={() => setActiveTab(tab)}
            style={{
              padding: '10px 20px',
              marginRight: '10px',
              backgroundColor: activeTab === tab ? '#007bff' : '#f8f9fa',
              color: activeTab === tab ? 'white' : 'black',
              border: 'none',
              borderRadius: '4px 4px 0 0',
              cursor: 'pointer'
            }}
          >
            {tab.charAt(0).toUpperCase() + tab.slice(1)}
          </button>
        ))}
      </div>

      {/* Profile Tab */}
      {activeTab === 'profile' && (
        <div>
          <h2>Profile Information</h2>
          
          {/* Update Username */}
          <form onSubmit={handleUpdateUsername} style={{ marginBottom: '30px' }}>
            <h3>Update Username</h3>
            <div style={{ marginBottom: '10px' }}>
              <label>New Username:</label>
              <input
                type="text"
                name="username"
                value={formData.username}
                onChange={handleInputChange}
                style={{ marginLeft: '10px', padding: '5px' }}
                required
              />
            </div>
            <div style={{ marginBottom: '10px' }}>
              <label>Current Password:</label>
              <input
                type="password"
                name="currentPassword"
                value={formData.currentPassword}
                onChange={handleInputChange}
                style={{ marginLeft: '10px', padding: '5px' }}
                required
              />
            </div>
            <button type="submit" disabled={profileLoading}>
              {profileLoading ? 'Updating...' : 'Update Username'}
            </button>
          </form>

          {/* Update Personal Info */}
          <form onSubmit={handleUpdatePersonalInfo} style={{ marginBottom: '30px' }}>
            <h3>Update Personal Information</h3>
            <div style={{ marginBottom: '10px' }}>
              <label>First Name:</label>
              <input
                type="text"
                name="firstname"
                value={formData.firstname}
                onChange={handleInputChange}
                style={{ marginLeft: '10px', padding: '5px' }}
                required
              />
            </div>
            <div style={{ marginBottom: '10px' }}>
              <label>Last Name:</label>
              <input
                type="text"
                name="lastname"
                value={formData.lastname}
                onChange={handleInputChange}
                style={{ marginLeft: '10px', padding: '5px' }}
                required
              />
            </div>
            <button type="submit" disabled={profileLoading}>
              {profileLoading ? 'Updating...' : 'Update Personal Info'}
            </button>
          </form>

          {/* Update Email */}
          <form onSubmit={handleUpdateEmail}>
            <h3>Update Email</h3>
            <div style={{ marginBottom: '10px' }}>
              <label>New Email:</label>
              <input
                type="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                style={{ marginLeft: '10px', padding: '5px' }}
                required
              />
            </div>
            <div style={{ marginBottom: '10px' }}>
              <label>Current Password:</label>
              <input
                type="password"
                name="currentPassword"
                value={formData.currentPassword}
                onChange={handleInputChange}
                style={{ marginLeft: '10px', padding: '5px' }}
                required
              />
            </div>
            <button type="submit" disabled={profileLoading}>
              {profileLoading ? 'Updating...' : 'Update Email'}
            </button>
          </form>
        </div>
      )}

      {/* Security Tab */}
      {activeTab === 'security' && (
        <div>
          <h2>Security Settings</h2>
          
          <form onSubmit={handleChangePassword}>
            <h3>Change Password</h3>
            <div style={{ marginBottom: '10px' }}>
              <label>Current Password:</label>
              <input
                type="password"
                name="currentPassword"
                value={formData.currentPassword}
                onChange={handleInputChange}
                style={{ marginLeft: '10px', padding: '5px' }}
                required
              />
            </div>
            <div style={{ marginBottom: '10px' }}>
              <label>New Password:</label>
              <input
                type="password"
                name="newPassword"
                value={formData.newPassword}
                onChange={handleInputChange}
                style={{ marginLeft: '10px', padding: '5px' }}
                required
              />
            </div>
            <div style={{ marginBottom: '10px' }}>
              <label>Confirm Password:</label>
              <input
                type="password"
                name="confirmPassword"
                value={formData.confirmPassword}
                onChange={handleInputChange}
                style={{ marginLeft: '10px', padding: '5px' }}
                required
              />
            </div>
            <button type="submit" disabled={profileLoading}>
              {profileLoading ? 'Changing...' : 'Change Password'}
            </button>
          </form>
        </div>
      )}

      {/* Vehicles Tab */}
      {activeTab === 'vehicles' && (
        <div>
          <h2>Vehicle Management</h2>
          
          {/* Add Vehicle */}
          <form onSubmit={handleAddVehicle} style={{ marginBottom: '30px' }}>
            <h3>Add Vehicle Registration</h3>
            <div style={{ marginBottom: '10px' }}>
              <label>Vehicle Registration:</label>
              <input
                type="text"
                name="vehicleRegistration"
                value={formData.vehicleRegistration}
                onChange={handleInputChange}
                style={{ marginLeft: '10px', padding: '5px' }}
                placeholder="e.g., ABC123"
                required
              />
            </div>
            <button type="submit" disabled={vehiclesLoading}>
              {vehiclesLoading ? 'Adding...' : 'Add Vehicle'}
            </button>
          </form>

          {/* Vehicle List */}
          <h3>Your Vehicles</h3>
          {vehiclesLoading ? (
            <p>Loading vehicles...</p>
          ) : vehicles.length > 0 ? (
            <ul>
              {vehicles.map((vehicle, index) => (
                <li key={index} style={{ marginBottom: '10px' }}>
                  <span style={{ marginRight: '10px' }}>{vehicle}</span>
                  <button
                    onClick={() => handleRemoveVehicle(vehicle)}
                    style={{
                      backgroundColor: '#dc3545',
                      color: 'white',
                      border: 'none',
                      padding: '5px 10px',
                      borderRadius: '4px',
                      cursor: 'pointer'
                    }}
                  >
                    Remove
                  </button>
                </li>
              ))}
            </ul>
          ) : (
            <p>No vehicles registered.</p>
          )}
        </div>
      )}

      {/* Error Display */}
      {(profileError || vehiclesError) && (
        <div style={{
          backgroundColor: '#f8d7da',
          color: '#721c24',
          padding: '10px',
          borderRadius: '4px',
          marginTop: '20px'
        }}>
          {profileError || vehiclesError}
          <button
            onClick={() => {
              clearProfileError();
              clearVehiclesError();
            }}
            style={{ marginLeft: '10px' }}
          >
            Clear
          </button>
        </div>
      )}
    </div>
  );
};

export default ProfileManagement;
