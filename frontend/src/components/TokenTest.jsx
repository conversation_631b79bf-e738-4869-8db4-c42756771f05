import React, { useState } from 'react';

const TokenTest = () => {
  const [testResult, setTestResult] = useState(null);

  const testTokenStorage = () => {
    // Simulate the backend response structure
    const mockResponse = {
      access_token: "eyJhbGciOiJIUzI1NiJ9.eyJhdXRob3JpdGllcyI6WyJEUklWRVIiXSwic3ViIjoidGVzdHVzZXIiLCJpYXQiOjE3NDc4MjI5MTksImV4cCI6MTc0NzkwOTMxOX0.test",
      refresh_token: "refresh_token_here"
    };

    console.log('🧪 Testing token storage with mock response:', mockResponse);

    // Test the token key detection logic
    const tokenKey = mockResponse.accessToken ? 'accessToken' :
                     mockResponse.access_token ? 'access_token' :
                     mockResponse.token ? 'token' :
                     mockResponse.jwt ? 'jwt' : null;

    console.log('🔍 Token key found:', tokenKey);

    if (tokenKey && mockResponse[tokenKey]) {
      const token = mockResponse[tokenKey];
      console.log('✅ Token found with key:', tokenKey);
      console.log('🔑 Token value:', token.substring(0, 20) + '...');
      
      // Store in localStorage
      localStorage.setItem('token', token);
      
      // Verify storage
      const storedToken = localStorage.getItem('token');
      console.log('✅ Token stored successfully:', !!storedToken);
      
      setTestResult({
        success: true,
        tokenKey: tokenKey,
        tokenStored: !!storedToken,
        message: `Token successfully stored with key: ${tokenKey}`
      });
    } else {
      setTestResult({
        success: false,
        message: 'No token found in mock response',
        availableKeys: Object.keys(mockResponse)
      });
    }
  };

  const clearStorage = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    setTestResult(null);
    console.log('🧹 Storage cleared');
  };

  return (
    <div style={{ padding: '20px', maxWidth: '600px', margin: '0 auto' }}>
      <h2>Token Storage Test</h2>
      
      <div style={{ marginBottom: '20px' }}>
        <button 
          onClick={testTokenStorage}
          style={{ marginRight: '10px', padding: '10px', backgroundColor: '#4CAF50', color: 'white', border: 'none', borderRadius: '4px' }}
        >
          Test Token Storage
        </button>
        
        <button 
          onClick={clearStorage}
          style={{ padding: '10px', backgroundColor: '#f44336', color: 'white', border: 'none', borderRadius: '4px' }}
        >
          Clear Storage
        </button>
      </div>
      
      {testResult && (
        <div style={{ 
          padding: '15px', 
          borderRadius: '4px',
          backgroundColor: testResult.success ? '#e8f5e8' : '#ffebee',
          border: `1px solid ${testResult.success ? '#4CAF50' : '#f44336'}`
        }}>
          <h3>{testResult.success ? '✅ Success' : '❌ Failed'}</h3>
          <p><strong>Message:</strong> {testResult.message}</p>
          {testResult.tokenKey && <p><strong>Token Key:</strong> {testResult.tokenKey}</p>}
          {testResult.tokenStored !== undefined && <p><strong>Token Stored:</strong> {testResult.tokenStored ? 'Yes' : 'No'}</p>}
          {testResult.availableKeys && (
            <p><strong>Available Keys:</strong> {testResult.availableKeys.join(', ')}</p>
          )}
        </div>
      )}
      
      <div style={{ marginTop: '20px', padding: '15px', backgroundColor: '#f5f5f5', borderRadius: '4px' }}>
        <h3>Expected Backend Response Format:</h3>
        <pre style={{ backgroundColor: '#fff', padding: '10px', borderRadius: '4px' }}>
{`{
  "access_token": "eyJhbGciOiJIUzI1NiJ9...",
  "refresh_token": "eyJhbGciOiJIUzI1NiJ9..."
}`}
        </pre>
        <p><strong>Note:</strong> The backend uses "access_token" (with underscore) due to the @JsonProperty annotation.</p>
      </div>
    </div>
  );
};

export default TokenTest;
