import React, { useState, useEffect } from 'react';
import { useValidation } from '../hooks/useProfile';

const UsernameChecker = ({ onUsernameChange, initialUsername = '' }) => {
  const [username, setUsername] = useState(initialUsername);
  const [availability, setAvailability] = useState(null);
  const [checkTimeout, setCheckTimeout] = useState(null);
  const { loading, checkUsername } = useValidation();

  useEffect(() => {
    setUsername(initialUsername);
  }, [initialUsername]);

  const handleUsernameChange = (e) => {
    const newUsername = e.target.value;
    setUsername(newUsername);
    setAvailability(null);

    // Clear previous timeout
    if (checkTimeout) {
      clearTimeout(checkTimeout);
    }

    // Only check if username is not empty and different from initial
    if (newUsername && newUsername !== initialUsername && newUsername.length >= 3) {
      // Debounce the API call
      const timeout = setTimeout(async () => {
        try {
          const isAvailable = await checkUsername(newUsername);
          setAvailability(isAvailable);
        } catch (error) {
          console.error('Error checking username:', error);
          setAvailability(null);
        }
      }, 500);

      setCheckTimeout(timeout);
    }

    // Call parent callback
    if (onUsernameChange) {
      onUsernameChange(newUsername, availability);
    }
  };

  const getStatusIcon = () => {
    if (loading) return '⏳';
    if (availability === true) return '✅';
    if (availability === false) return '❌';
    return '';
  };

  const getStatusMessage = () => {
    if (loading) return 'Checking availability...';
    if (availability === true) return 'Username is available';
    if (availability === false) return 'Username is already taken';
    if (username && username.length < 3) return 'Username must be at least 3 characters';
    return '';
  };

  const getStatusColor = () => {
    if (loading) return '#6c757d';
    if (availability === true) return '#28a745';
    if (availability === false) return '#dc3545';
    return '#6c757d';
  };

  return (
    <div style={{ marginBottom: '10px' }}>
      <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
        <input
          type="text"
          value={username}
          onChange={handleUsernameChange}
          placeholder="Enter username"
          style={{
            padding: '8px',
            border: `2px solid ${getStatusColor()}`,
            borderRadius: '4px',
            fontSize: '14px'
          }}
        />
        <span style={{ fontSize: '16px' }}>{getStatusIcon()}</span>
      </div>
      {getStatusMessage() && (
        <div style={{
          fontSize: '12px',
          color: getStatusColor(),
          marginTop: '5px'
        }}>
          {getStatusMessage()}
        </div>
      )}
    </div>
  );
};

export default UsernameChecker;
