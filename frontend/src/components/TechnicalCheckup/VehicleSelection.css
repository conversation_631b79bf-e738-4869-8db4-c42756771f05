/* Vehicle Selection Styles */
.vehicle-selection-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background-color: #f8f9fa;
  min-height: 100vh;
}

/* Header Section */
.header-section {
  text-align: center;
  margin-bottom: 40px;
  padding: 30px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.header-section h1 {
  margin: 0 0 10px 0;
  font-size: 32px;
  font-weight: 600;
}

.header-section p {
  margin: 0;
  font-size: 16px;
  opacity: 0.9;
}

/* Loading State */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: #6c757d;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e9ecef;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

.loading-spinner-small {
  width: 20px;
  height: 20px;
  border: 2px solid #e9ecef;
  border-top: 2px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error Message */
.error-message {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
  border-radius: 8px;
  margin-bottom: 20px;
  font-weight: 500;
}

.error-message button {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  padding: 0;
  margin-left: 10px;
  opacity: 0.7;
}

.error-message button:hover {
  opacity: 1;
}

/* Content Section */
.content-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  margin-bottom: 30px;
}

/* Vehicle List Section */
.vehicle-list-section h2 {
  margin: 0 0 20px 0;
  font-size: 24px;
  color: #495057;
}

.vehicle-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 16px;
}

.vehicle-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.2s ease;
  border: 2px solid transparent;
  position: relative;
}

.vehicle-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.vehicle-card.selected {
  border-color: #007bff;
  background: #f8f9ff;
}

.vehicle-icon {
  font-size: 32px;
  text-align: center;
  margin-bottom: 12px;
}

.vehicle-info h3 {
  margin: 0 0 4px 0;
  font-size: 18px;
  font-weight: 600;
  color: #495057;
  text-align: center;
}

.vehicle-info p {
  margin: 0;
  font-size: 14px;
  color: #6c757d;
  text-align: center;
}

.selection-indicator {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 24px;
  height: 24px;
  background: #007bff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 14px;
  font-weight: bold;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 20px;
}

.empty-state h3 {
  margin: 0 0 10px 0;
  font-size: 24px;
  color: #495057;
}

.empty-state p {
  margin: 0 0 20px 0;
  color: #6c757d;
}

/* Vehicle Details Section */
.vehicle-details-section h2 {
  margin: 0 0 20px 0;
  font-size: 24px;
  color: #495057;
}

.vehicle-details-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.details-header {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e9ecef;
}

.vehicle-icon-large {
  font-size: 48px;
  margin-right: 16px;
}

.vehicle-title h3 {
  margin: 0 0 4px 0;
  font-size: 24px;
  color: #495057;
}

.vehicle-title p {
  margin: 0;
  color: #6c757d;
  font-size: 16px;
}

.details-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detail-item .label {
  font-size: 12px;
  color: #6c757d;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.detail-item .value {
  font-size: 14px;
  color: #495057;
  font-weight: 500;
}

.details-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #6c757d;
}

.details-error {
  text-align: center;
  padding: 40px;
  color: #dc3545;
}

/* Action Section */
.action-section {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #e9ecef;
}

/* Navigation Section */
.navigation-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 0;
}

/* Buttons */
.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-primary:hover {
  background: #0056b3;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #545b62;
}

.btn-large {
  padding: 16px 32px;
  font-size: 16px;
}

.btn-icon {
  font-size: 16px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .vehicle-selection-container {
    padding: 16px;
  }

  .content-section {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .vehicle-grid {
    grid-template-columns: 1fr;
  }

  .details-grid {
    grid-template-columns: 1fr;
  }

  .navigation-section {
    flex-direction: column;
    gap: 12px;
  }

  .btn {
    width: 100%;
  }

  .header-section {
    padding: 20px;
  }

  .header-section h1 {
    font-size: 24px;
  }
}
