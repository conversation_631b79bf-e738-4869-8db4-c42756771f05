/* Checkup Form Styles */
.checkup-form-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background-color: #f8f9fa;
  min-height: 100vh;
}

/* Header Section */
.header-section {
  text-align: center;
  margin-bottom: 40px;
  padding: 30px;
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.header-section h1 {
  margin: 0 0 10px 0;
  font-size: 32px;
  font-weight: 600;
}

.header-section p {
  margin: 0;
  font-size: 16px;
  opacity: 0.9;
}

/* Success State */
.success-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.success-icon {
  font-size: 64px;
  margin-bottom: 20px;
}

.success-state h2 {
  margin: 0 0 10px 0;
  font-size: 28px;
  color: #28a745;
}

.success-state p {
  margin: 0 0 10px 0;
  color: #6c757d;
  font-size: 16px;
}

/* Error Message */
.error-message {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
  border-radius: 8px;
  margin-bottom: 20px;
  font-weight: 500;
}

.error-message button {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  padding: 0;
  margin-left: 10px;
  opacity: 0.7;
}

.error-message button:hover {
  opacity: 1;
}

/* Form Section */
.form-section {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 30px;
}

.form-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.form-header {
  padding: 24px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.form-header h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  color: #495057;
}

.form-header p {
  margin: 0;
  color: #6c757d;
  font-size: 14px;
}

/* Form Styles */
.checkup-form {
  padding: 24px;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-group label {
  margin-bottom: 8px;
  font-weight: 500;
  color: #495057;
  font-size: 14px;
}

.form-input, .form-textarea {
  padding: 12px 16px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.2s ease;
  font-family: inherit;
}

.form-input:focus, .form-textarea:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.form-textarea {
  resize: vertical;
  min-height: 100px;
}

.form-input::placeholder, .form-textarea::placeholder {
  color: #adb5bd;
}

/* Form Actions */
.form-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #e9ecef;
}

/* Buttons */
.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #0056b3;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background: #545b62;
}

/* Loading Spinner */
.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e9ecef;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 20px auto;
}

.loading-spinner-small {
  width: 16px;
  height: 16px;
  border: 2px solid #e9ecef;
  border-top: 2px solid #ffffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Info Panel */
.info-panel {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  height: fit-content;
}

.info-panel h3 {
  margin: 0 0 20px 0;
  font-size: 20px;
  color: #495057;
}

/* Step List */
.step-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.step {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.step.completed {
  background: #d4edda;
  border: 1px solid #c3e6cb;
}

.step.active {
  background: #cce7ff;
  border: 1px solid #99d3ff;
}

.step:not(.completed):not(.active) {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
}

.step-number {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 14px;
  flex-shrink: 0;
}

.step.completed .step-number {
  background: #28a745;
  color: white;
}

.step.active .step-number {
  background: #007bff;
  color: white;
}

.step:not(.completed):not(.active) .step-number {
  background: #e9ecef;
  color: #6c757d;
}

.step-content h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  color: #495057;
}

.step-content p {
  margin: 0;
  font-size: 14px;
  color: #6c757d;
}

/* Responsive Design */
@media (max-width: 768px) {
  .checkup-form-container {
    padding: 16px;
  }

  .form-section {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }

  .form-actions {
    flex-direction: column;
    gap: 12px;
  }

  .btn {
    width: 100%;
  }

  .header-section {
    padding: 20px;
  }

  .header-section h1 {
    font-size: 24px;
  }

  .step-list {
    gap: 12px;
  }

  .step {
    padding: 12px;
  }
}
