import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { createTechnicalCheckup } from '../../Services/technicalCheckupService';
import './CheckupForm.css';

const CheckupForm = () => {
  const location = useLocation();
  const navigate = useNavigate();
  
  const [formData, setFormData] = useState({
    vehicleRegistration: '',
    vehicleType: '',
    vehicleModel: '',
    vehicleYear: '',
    chassisNumber: '',
    vehicleColor: '',
    vehicleFuelType: '',
    notes: ''
  });
  
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);

  useEffect(() => {
    // Auto-fill form with vehicle data from previous page
    if (location.state && location.state.vehicle) {
      const vehicle = location.state.vehicle;
      setFormData({
        vehicleRegistration: vehicle.registration || vehicle.immat || location.state.selectedRegistration || '',
        vehicleType: vehicle.vehicleType || '',
        vehicleModel: vehicle.vehicleModel || '',
        vehicleYear: vehicle.vehicleYear || '',
        chassisNumber: vehicle.chassisNumber || '',
        vehicleColor: vehicle.vehicleColor || '',
        vehicleFuelType: vehicle.vehicleFuelType || '',
        notes: ''
      });
    } else {
      // If no vehicle data, redirect back to selection
      navigate('/technical-checkup');
    }
  }, [location.state, navigate]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    try {
      setLoading(true);
      setError(null);
      
      const checkupData = {
        ...formData,
        status: 'PENDING'
      };
      
      const result = await createTechnicalCheckup(checkupData);
      
      setSuccess(true);
      
      // Navigate to center selection after 2 seconds
      setTimeout(() => {
        navigate('/technical-checkup/center-selection', {
          state: {
            checkupId: result.id,
            vehicle: formData
          }
        });
      }, 2000);
      
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleBack = () => {
    navigate('/technical-checkup');
  };

  if (success) {
    return (
      <div className="checkup-form-container">
        <div className="success-state">
          <div className="success-icon">✅</div>
          <h2>Checkup Request Created!</h2>
          <p>Your technical checkup request has been submitted successfully.</p>
          <p>Redirecting to center selection...</p>
          <div className="loading-spinner"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="checkup-form-container">
      <div className="header-section">
        <h1>Technical Checkup Form</h1>
        <p>Review and confirm your vehicle information</p>
      </div>

      {error && (
        <div className="error-message">
          <span>⚠️ {error}</span>
          <button onClick={() => setError(null)}>×</button>
        </div>
      )}

      <div className="form-section">
        <div className="form-card">
          <div className="form-header">
            <h2>Vehicle Information</h2>
            <p>This information has been auto-filled from your vehicle registration</p>
          </div>

          <form onSubmit={handleSubmit} className="checkup-form">
            <div className="form-grid">
              <div className="form-group">
                <label htmlFor="vehicleRegistration">
                  Vehicle Registration *
                </label>
                <input
                  type="text"
                  id="vehicleRegistration"
                  name="vehicleRegistration"
                  value={formData.vehicleRegistration}
                  onChange={handleInputChange}
                  required
                  className="form-input"
                />
              </div>

              <div className="form-group">
                <label htmlFor="vehicleType">
                  Vehicle Type
                </label>
                <select
                  id="vehicleType"
                  name="vehicleType"
                  value={formData.vehicleType}
                  onChange={handleInputChange}
                  className="form-input"
                >
                  <option value="">Select Type</option>
                  <option value="Car">Car</option>
                  <option value="Motorcycle">Motorcycle</option>
                  <option value="Truck">Truck</option>
                  <option value="Van">Van</option>
                  <option value="Bus">Bus</option>
                  <option value="Other">Other</option>
                </select>
              </div>

              <div className="form-group">
                <label htmlFor="vehicleModel">
                  Vehicle Model
                </label>
                <input
                  type="text"
                  id="vehicleModel"
                  name="vehicleModel"
                  value={formData.vehicleModel}
                  onChange={handleInputChange}
                  className="form-input"
                  placeholder="e.g., Toyota Corolla"
                />
              </div>

              <div className="form-group">
                <label htmlFor="vehicleYear">
                  Manufacturing Year
                </label>
                <input
                  type="text"
                  id="vehicleYear"
                  name="vehicleYear"
                  value={formData.vehicleYear}
                  onChange={handleInputChange}
                  className="form-input"
                  placeholder="e.g., 2020"
                />
              </div>

              <div className="form-group">
                <label htmlFor="chassisNumber">
                  Chassis Number
                </label>
                <input
                  type="text"
                  id="chassisNumber"
                  name="chassisNumber"
                  value={formData.chassisNumber}
                  onChange={handleInputChange}
                  className="form-input"
                  placeholder="Vehicle Identification Number (VIN)"
                />
              </div>

              <div className="form-group">
                <label htmlFor="vehicleColor">
                  Vehicle Color
                </label>
                <input
                  type="text"
                  id="vehicleColor"
                  name="vehicleColor"
                  value={formData.vehicleColor}
                  onChange={handleInputChange}
                  className="form-input"
                  placeholder="e.g., White"
                />
              </div>

              <div className="form-group">
                <label htmlFor="vehicleFuelType">
                  Fuel Type
                </label>
                <select
                  id="vehicleFuelType"
                  name="vehicleFuelType"
                  value={formData.vehicleFuelType}
                  onChange={handleInputChange}
                  className="form-input"
                >
                  <option value="">Select Fuel Type</option>
                  <option value="Petrol">Petrol</option>
                  <option value="Diesel">Diesel</option>
                  <option value="Electric">Electric</option>
                  <option value="Hybrid">Hybrid</option>
                  <option value="LPG">LPG</option>
                  <option value="CNG">CNG</option>
                </select>
              </div>
            </div>

            <div className="form-group full-width">
              <label htmlFor="notes">
                Additional Notes (Optional)
              </label>
              <textarea
                id="notes"
                name="notes"
                value={formData.notes}
                onChange={handleInputChange}
                className="form-textarea"
                rows="4"
                placeholder="Any additional information about your vehicle or special requirements..."
              />
            </div>

            <div className="form-actions">
              <button 
                type="button" 
                className="btn btn-secondary"
                onClick={handleBack}
                disabled={loading}
              >
                ← Back to Vehicle Selection
              </button>
              
              <button 
                type="submit" 
                className="btn btn-primary"
                disabled={loading}
              >
                {loading ? (
                  <>
                    <div className="loading-spinner-small"></div>
                    Creating Request...
                  </>
                ) : (
                  <>
                    Continue to Center Selection →
                  </>
                )}
              </button>
            </div>
          </form>
        </div>

        {/* Information Panel */}
        <div className="info-panel">
          <h3>What happens next?</h3>
          <div className="step-list">
            <div className="step completed">
              <div className="step-number">1</div>
              <div className="step-content">
                <h4>Vehicle Selected</h4>
                <p>You've selected your vehicle for technical checkup</p>
              </div>
            </div>
            
            <div className="step active">
              <div className="step-number">2</div>
              <div className="step-content">
                <h4>Confirm Details</h4>
                <p>Review and confirm your vehicle information</p>
              </div>
            </div>
            
            <div className="step">
              <div className="step-number">3</div>
              <div className="step-content">
                <h4>Choose Center</h4>
                <p>Select a technical center near you</p>
              </div>
            </div>
            
            <div className="step">
              <div className="step-number">4</div>
              <div className="step-content">
                <h4>Book Appointment</h4>
                <p>Choose your preferred date and time</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CheckupForm;
