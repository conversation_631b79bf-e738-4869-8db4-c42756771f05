import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { getDriverVehicles, getVehicleByRegistration } from '../../Services/technicalCheckupService';
import './VehicleSelection.css';

const VehicleSelection = () => {
  const [vehicles, setVehicles] = useState([]);
  const [selectedVehicle, setSelectedVehicle] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [vehicleDetails, setVehicleDetails] = useState(null);
  const [loadingDetails, setLoadingDetails] = useState(false);

  const navigate = useNavigate();

  useEffect(() => {
    fetchVehicles();
  }, []);

  const fetchVehicles = async () => {
    try {
      setLoading(true);
      console.log('🔄 VehicleSelection: Starting to fetch vehicles...');

      const vehicleData = await getDriverVehicles();

      console.log('📊 VehicleSelection: Received vehicle data:', vehicleData);
      console.log('📊 VehicleSelection: Data type:', typeof vehicleData);
      console.log('📊 VehicleSelection: Is array:', Array.isArray(vehicleData));
      console.log('📊 VehicleSelection: Data length:', vehicleData?.length);

      setVehicles(vehicleData);
      console.log('✅ VehicleSelection: Vehicles state updated');
    } catch (err) {
      console.error('❌ VehicleSelection: Error fetching vehicles:', err);
      console.error('❌ VehicleSelection: Error details:', {
        message: err.message,
        stack: err.stack,
        name: err.name
      });
      setError(err.message || 'Failed to fetch vehicles');
    } finally {
      setLoading(false);
    }
  };

  const handleVehicleSelect = async (vehicle) => {
    try {
      setSelectedVehicle(vehicle);
      setLoadingDetails(true);
      setError(null);

      // Fetch detailed vehicle information
      const details = await getVehicleByRegistration(vehicle.registration || vehicle);
      setVehicleDetails(details);
    } catch (err) {
      setError(err.message);
      setVehicleDetails(null);
    } finally {
      setLoadingDetails(false);
    }
  };

  const handleProceedToCheckup = () => {
    if (selectedVehicle && vehicleDetails) {
      // Navigate to checkup form with vehicle data
      navigate('/technical-checkup/form', {
        state: {
          vehicle: vehicleDetails,
          selectedRegistration: selectedVehicle
        }
      });
    }
  };

  if (loading) {
    return (
      <div className="vehicle-selection-container">
        <div className="loading-state">
          <div className="loading-spinner"></div>
          <p>Loading your vehicles...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="vehicle-selection-container">
      <div className="header-section">
        <h1>Technical Checkup</h1>
        <p>Select a vehicle to schedule your technical checkup</p>
      </div>

      {error && (
        <div className="error-message">
          <span>⚠️ {error}</span>
          <button onClick={() => setError(null)}>×</button>
        </div>
      )}

      <div className="content-section">
        {/* Vehicle List */}
        <div className="vehicle-list-section">
          <h2>Your Registered Vehicles</h2>

          {!vehicles || vehicles.length === 0 ? (
            <div className="empty-state">
              <div className="empty-icon">🚗</div>
              <h3>No Vehicles Found</h3>
              <p>You don't have any registered vehicles yet.</p>
              <p><small>Debug: vehicles = {JSON.stringify(vehicles)}</small></p>
              <button
                className="btn btn-primary"
                onClick={() => navigate('/profile')}
              >
                Add Vehicle to Profile
              </button>
            </div>
          ) : (
            <div className="vehicle-grid">
              {vehicles.map((vehicle, index) => (
                <div
                  key={index}
                  className={`vehicle-card ${selectedVehicle === vehicle ? 'selected' : ''}`}
                  onClick={() => handleVehicleSelect(vehicle)}
                >
                  <div className="vehicle-icon">🚗</div>
                  <div className="vehicle-info">
                    <h3>{typeof vehicle === 'string' ? vehicle : vehicle.registration}</h3>
                    <p>Registration Number</p>
                  </div>
                  <div className="selection-indicator">
                    {selectedVehicle === vehicle && <span>✓</span>}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Vehicle Details */}
        {selectedVehicle && (
          <div className="vehicle-details-section">
            <h2>Vehicle Details</h2>

            {loadingDetails ? (
              <div className="details-loading">
                <div className="loading-spinner-small"></div>
                <p>Loading vehicle details...</p>
              </div>
            ) : vehicleDetails ? (
              <div className="vehicle-details-card">
                <div className="details-header">
                  <div className="vehicle-icon-large">🚗</div>
                  <div className="vehicle-title">
                    <h3>{vehicleDetails.registration || selectedVehicle}</h3>
                    <p>{vehicleDetails.vehicleModel || 'Vehicle Model'}</p>
                  </div>
                </div>

                <div className="details-grid">
                  <div className="detail-item">
                    <span className="label">Registration:</span>
                    <span className="value">{vehicleDetails.registration || selectedVehicle}</span>
                  </div>
                  <div className="detail-item">
                    <span className="label">Type:</span>
                    <span className="value">{vehicleDetails.vehicleType || 'Not specified'}</span>
                  </div>
                  <div className="detail-item">
                    <span className="label">Model:</span>
                    <span className="value">{vehicleDetails.vehicleModel || 'Not specified'}</span>
                  </div>
                  <div className="detail-item">
                    <span className="label">Year:</span>
                    <span className="value">{vehicleDetails.vehicleYear || 'Not specified'}</span>
                  </div>
                  <div className="detail-item">
                    <span className="label">Chassis Number:</span>
                    <span className="value">{vehicleDetails.chassisNumber || 'Not specified'}</span>
                  </div>
                  <div className="detail-item">
                    <span className="label">Color:</span>
                    <span className="value">{vehicleDetails.vehicleColor || 'Not specified'}</span>
                  </div>
                  <div className="detail-item">
                    <span className="label">Fuel Type:</span>
                    <span className="value">{vehicleDetails.vehicleFuelType || 'Not specified'}</span>
                  </div>
                </div>

                <div className="action-section">
                  <button
                    className="btn btn-primary btn-large"
                    onClick={handleProceedToCheckup}
                  >
                    Proceed with This Vehicle
                    <span className="btn-icon">→</span>
                  </button>
                </div>
              </div>
            ) : (
              <div className="details-error">
                <p>Unable to load vehicle details. Please try selecting the vehicle again.</p>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Navigation */}
      <div className="navigation-section">
        <button
          className="btn btn-secondary"
          onClick={() => navigate('/dashboard')}
        >
          ← Back to Dashboard
        </button>

        {selectedVehicle && vehicleDetails && (
          <button
            className="btn btn-primary"
            onClick={handleProceedToCheckup}
          >
            Continue to Checkup Form →
          </button>
        )}
      </div>
    </div>
  );
};

export default VehicleSelection;
