import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { getTechnicalCenters, getAvailableDates } from '../../Services/technicalCheckupService';
import './CenterSelection.css';

const CenterSelection = () => {
  const location = useLocation();
  const navigate = useNavigate();
  
  const [centers, setCenters] = useState([]);
  const [selectedCenter, setSelectedCenter] = useState(null);
  const [availableDates, setAvailableDates] = useState([]);
  const [selectedDate, setSelectedDate] = useState(null);
  const [loading, setLoading] = useState(true);
  const [loadingDates, setLoadingDates] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchCenters();
  }, []);

  const fetchCenters = async () => {
    try {
      setLoading(true);
      const centerData = await getTechnicalCenters();
      setCenters(centerData);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleCenterSelect = async (center) => {
    try {
      setSelectedCenter(center);
      setLoadingDates(true);
      setError(null);
      
      const dates = await getAvailableDates(center.id);
      setAvailableDates(dates);
    } catch (err) {
      setError(err.message);
      setAvailableDates([]);
    } finally {
      setLoadingDates(false);
    }
  };

  const handleDateSelect = (date) => {
    setSelectedDate(date);
  };

  const handleConfirmBooking = () => {
    if (selectedCenter && selectedDate) {
      // Navigate to confirmation page or complete booking
      navigate('/technical-checkup/confirmation', {
        state: {
          checkupId: location.state?.checkupId,
          vehicle: location.state?.vehicle,
          center: selectedCenter,
          date: selectedDate
        }
      });
    }
  };

  if (loading) {
    return (
      <div className="center-selection-container">
        <div className="loading-state">
          <div className="loading-spinner"></div>
          <p>Loading technical centers...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="center-selection-container">
      <div className="header-section">
        <h1>Select Technical Center</h1>
        <p>Choose a center and available date for your technical checkup</p>
      </div>

      {error && (
        <div className="error-message">
          <span>⚠️ {error}</span>
          <button onClick={() => setError(null)}>×</button>
        </div>
      )}

      <div className="selection-content">
        {/* Centers List */}
        <div className="centers-section">
          <h2>Available Centers</h2>
          <div className="centers-grid">
            {centers.map((center) => (
              <div
                key={center.id}
                className={`center-card ${selectedCenter?.id === center.id ? 'selected' : ''}`}
                onClick={() => handleCenterSelect(center)}
              >
                <div className="center-icon">🏢</div>
                <div className="center-info">
                  <h3>{center.name}</h3>
                  <p className="center-address">{center.address}</p>
                  <p className="center-city">{center.city}</p>
                  <div className="center-details">
                    <span>📞 {center.phone}</span>
                    <span>🕒 {center.openingTime} - {center.closingTime}</span>
                  </div>
                </div>
                {selectedCenter?.id === center.id && (
                  <div className="selection-indicator">✓</div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Date Selection */}
        {selectedCenter && (
          <div className="dates-section">
            <h2>Available Dates</h2>
            {loadingDates ? (
              <div className="dates-loading">
                <div className="loading-spinner-small"></div>
                <p>Loading available dates...</p>
              </div>
            ) : availableDates.length > 0 ? (
              <div className="dates-grid">
                {availableDates.map((date, index) => (
                  <div
                    key={index}
                    className={`date-card ${selectedDate === date ? 'selected' : ''}`}
                    onClick={() => handleDateSelect(date)}
                  >
                    <div className="date-day">{new Date(date.date).getDate()}</div>
                    <div className="date-month">
                      {new Date(date.date).toLocaleDateString('en', { month: 'short' })}
                    </div>
                    <div className="date-time">{date.time}</div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="no-dates">
                <p>No available dates for this center. Please select another center.</p>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Navigation */}
      <div className="navigation-section">
        <button 
          className="btn btn-secondary"
          onClick={() => navigate('/technical-checkup/form')}
        >
          ← Back to Form
        </button>
        
        {selectedCenter && selectedDate && (
          <button 
            className="btn btn-primary"
            onClick={handleConfirmBooking}
          >
            Confirm Booking →
          </button>
        )}
      </div>

      {/* Placeholder Message */}
      <div className="placeholder-message">
        <div className="placeholder-icon">🚧</div>
        <h3>Center & Date Selection</h3>
        <p>This is a placeholder for the center and date selection functionality.</p>
        <p>The backend integration for technical centers and available slots will be implemented next.</p>
      </div>
    </div>
  );
};

export default CenterSelection;
