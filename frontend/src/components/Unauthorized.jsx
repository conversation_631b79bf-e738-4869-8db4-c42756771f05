import React from 'react';
import { Link } from 'react-router-dom';
import { getUserRole } from '../utils/jwtUtils';

const Unauthorized = () => {
  const role = getUserRole();
  
  return (
    <div className="unauthorized-page">
      <h1>Access Denied</h1>
      <div className="error-icon">🚫</div>
      
      <p className="error-message">
        You don't have permission to access the requested page.
      </p>
      
      <div className="role-info">
        <p>Your current role: <strong>{role || 'No role found'}</strong></p>
        <p>The requested page requires a different role.</p>
      </div>
      
      <div className="actions">
        <Link to="/" className="home-link">Go to Home</Link>
        <Link to="/login" className="login-link">Login with a Different Account</Link>
      </div>
    </div>
  );
};

export default Unauthorized;

// Add this CSS to your styles
/*
.unauthorized-page {
  max-width: 600px;
  margin: 50px auto;
  padding: 30px;
  text-align: center;
  background-color: #f8f8f8;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.error-icon {
  font-size: 60px;
  margin: 20px 0;
}

.error-message {
  font-size: 18px;
  color: #d32f2f;
  margin-bottom: 20px;
}

.role-info {
  background-color: #f1f1f1;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 30px;
}

.actions {
  display: flex;
  justify-content: center;
  gap: 20px;
}

.home-link, .login-link {
  display: inline-block;
  padding: 10px 20px;
  background-color: #2196f3;
  color: white;
  text-decoration: none;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.login-link {
  background-color: #4caf50;
}

.home-link:hover {
  background-color: #1976d2;
}

.login-link:hover {
  background-color: #388e3c;
}
*/
