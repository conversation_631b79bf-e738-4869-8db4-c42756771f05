import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { logout } from '../Services/authservice';
import ProfileCard from './ProfileCard';
import './Navigation.css';

const Navigation = () => {
  const [showProfileDropdown, setShowProfileDropdown] = useState(false);
  const navigate = useNavigate();

  const handleLogout = async () => {
    try {
      await logout();
      navigate('/login');
    } catch (error) {
      console.error('Logout error:', error);
      // Force logout even if API call fails
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      navigate('/login');
    }
  };

  return (
    <nav className="navigation">
      <div className="nav-container">
        {/* Logo */}
        <div className="nav-logo">
          <Link to="/" className="logo-link">
            🚗 TaxStick
          </Link>
        </div>

        {/* Navigation Links */}
        <div className="nav-links">
          <Link to="/dashboard" className="nav-link">
            <span className="nav-icon">📊</span>
            Dashboard
          </Link>
          <Link to="/profile" className="nav-link">
            <span className="nav-icon">👤</span>
            Profile
          </Link>
          <Link to="/vehicles" className="nav-link">
            <span className="nav-icon">🚗</span>
            Vehicles
          </Link>
          <Link to="/technical-checkup" className="nav-link">
            <span className="nav-icon">🔧</span>
            Technical Checkup
          </Link>
        </div>

        {/* Profile Dropdown */}
        <div className="nav-profile">
          <button
            className="profile-trigger"
            onClick={() => setShowProfileDropdown(!showProfileDropdown)}
          >
            <span className="profile-avatar-small">
              👤
            </span>
            <span className="dropdown-arrow">▼</span>
          </button>

          {showProfileDropdown && (
            <div className="profile-dropdown">
              <div className="dropdown-header">
                <ProfileCard compact={true} />
              </div>

              <div className="dropdown-menu">
                <Link
                  to="/profile"
                  className="dropdown-item"
                  onClick={() => setShowProfileDropdown(false)}
                >
                  <span className="dropdown-icon">⚙️</span>
                  Manage Profile
                </Link>
                <Link
                  to="/settings"
                  className="dropdown-item"
                  onClick={() => setShowProfileDropdown(false)}
                >
                  <span className="dropdown-icon">🔧</span>
                  Settings
                </Link>
                <div className="dropdown-divider"></div>
                <button
                  className="dropdown-item logout-item"
                  onClick={handleLogout}
                >
                  <span className="dropdown-icon">🚪</span>
                  Logout
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Overlay to close dropdown */}
      {showProfileDropdown && (
        <div
          className="dropdown-overlay"
          onClick={() => setShowProfileDropdown(false)}
        ></div>
      )}
    </nav>
  );
};

export default Navigation;
