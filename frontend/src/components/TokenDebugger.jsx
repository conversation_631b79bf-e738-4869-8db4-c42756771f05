import React, { useState, useEffect } from 'react';
import { decodeToken, getUserRole, getUserInfo } from '../utils/jwtUtils';

const TokenDebugger = () => {
  const [tokenData, setTokenData] = useState({
    token: null,
    decoded: null,
    role: null,
    userInfo: null,
    error: null
  });

  useEffect(() => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        setTokenData({
          token: null,
          decoded: null,
          role: null,
          userInfo: null,
          error: 'No token found in localStorage'
        });
        return;
      }

      const decoded = decodeToken(token);
      const role = getUserRole();
      const userInfo = getUserInfo();

      setTokenData({
        token,
        decoded,
        role,
        userInfo,
        error: null
      });
    } catch (error) {
      setTokenData(prev => ({
        ...prev,
        error: `Error analyzing token: ${error.message}`
      }));
    }
  }, []);

  return (
    <div className="token-debugger">
      <h2>JWT Token Debugger</h2>
      
      {tokenData.error ? (
        <div className="error-message">
          {tokenData.error}
        </div>
      ) : (
        <>
          <div className="token-section">
            <h3>Token</h3>
            <div className="token-value">
              {tokenData.token ? (
                <div className="token-preview">
                  {`${tokenData.token.substring(0, 20)}...`}
                </div>
              ) : 'No token found'}
            </div>
          </div>
          
          <div className="token-section">
            <h3>Decoded Token</h3>
            <pre className="token-decoded">
              {tokenData.decoded ? 
                JSON.stringify(tokenData.decoded, null, 2) : 
                'No decoded token data'
              }
            </pre>
          </div>
          
          <div className="token-section">
            <h3>User Role</h3>
            <div className="user-role">
              {tokenData.role || 'No role found in token'}
            </div>
          </div>
          
          <div className="token-section">
            <h3>User Info</h3>
            <pre className="user-info">
              {tokenData.userInfo ? 
                JSON.stringify(tokenData.userInfo, null, 2) : 
                'No user info found'
              }
            </pre>
          </div>
        </>
      )}
    </div>
  );
};

export default TokenDebugger;

// Add this CSS to your styles
/*
.token-debugger {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  background-color: #f5f5f5;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.token-section {
  margin-bottom: 20px;
  padding: 15px;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.token-section h3 {
  margin-top: 0;
  color: #333;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.token-value, .user-role {
  font-family: monospace;
  word-break: break-all;
  padding: 10px;
  background-color: #f8f8f8;
  border-radius: 4px;
}

.token-decoded, .user-info {
  background-color: #f8f8f8;
  padding: 15px;
  border-radius: 4px;
  overflow-x: auto;
  white-space: pre-wrap;
}

.error-message {
  color: #d32f2f;
  padding: 15px;
  background-color: #ffebee;
  border-radius: 4px;
  margin-bottom: 20px;
}
*/
