import React, { useState, useEffect } from 'react';
import { useProfile } from '../hooks/useProfile';
import './ProfileCard.css';

const ProfileCard = ({ compact = false }) => {
  const { profile, loading, fetchProfile } = useProfile();

  useEffect(() => {
    if (!profile) {
      fetchProfile();
    }
  }, [profile, fetchProfile]);

  if (loading && !profile) {
    return (
      <div className={`profile-card ${compact ? 'compact' : ''}`}>
        <div className="profile-card-loading">
          <div className="loading-spinner-small"></div>
          <span>Loading...</span>
        </div>
      </div>
    );
  }

  if (!profile) {
    return (
      <div className={`profile-card ${compact ? 'compact' : ''}`}>
        <div className="profile-card-error">
          <span>⚠️ Unable to load profile</span>
        </div>
      </div>
    );
  }

  return (
    <div className={`profile-card ${compact ? 'compact' : ''}`}>
      <div className="profile-card-header">
        <div className="profile-card-avatar">
          {profile.firstname?.charAt(0)}{profile.lastname?.charAt(0)}
        </div>
        <div className="profile-card-info">
          <h3 className="profile-card-name">
            {profile.firstname} {profile.lastname}
          </h3>
          <p className="profile-card-email">{profile.email}</p>
          {!compact && (
            <div className="profile-card-badges">
              <span className={`profile-badge ${profile.emailVerified ? 'verified' : 'unverified'}`}>
                {profile.emailVerified ? '✓ Verified' : '⚠ Unverified'}
              </span>
            </div>
          )}
        </div>
      </div>
      
      {!compact && (
        <div className="profile-card-stats">
          <div className="stat-item">
            <span className="stat-label">Username</span>
            <span className="stat-value">{profile.username}</span>
          </div>
          <div className="stat-item">
            <span className="stat-label">Member Since</span>
            <span className="stat-value">
              {profile.createdDate ? new Date(profile.createdDate).toLocaleDateString() : 'Unknown'}
            </span>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProfileCard;
