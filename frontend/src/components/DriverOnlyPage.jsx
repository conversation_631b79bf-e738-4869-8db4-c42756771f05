import React from 'react';
import { getUserRole } from '../utils/jwtUtils';

const DriverOnlyPage = () => {
  const role = getUserRole();
  
  return (
    <div className="driver-only-page">
      <h1>Driver Only Page</h1>
      <p>This page should only be accessible to users with the DRIVER role.</p>
      
      <div className="role-info">
        <h2>Your Role</h2>
        <p>Current role: <strong>{role || 'No role found'}</strong></p>
        
        {role === 'DRIVER' ? (
          <div className="success-message">
            ✅ You have the correct role to access this page.
          </div>
        ) : (
          <div className="error-message">
            ❌ You do not have the correct role to access this page.
            This content should not be visible due to the RoleProtectedRoute.
          </div>
        )}
      </div>
      
      <div className="driver-content">
        <h2>Driver-Specific Content</h2>
        <p>This content is only relevant for drivers.</p>
        <ul>
          <li>View your vehicle registrations</li>
          <li>Manage your driver profile</li>
          <li>Access driver-specific features</li>
        </ul>
      </div>
    </div>
  );
};

export default DriverOnlyPage;
