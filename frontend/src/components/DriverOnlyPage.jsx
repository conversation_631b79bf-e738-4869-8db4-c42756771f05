import {
    Container, Typo<PERSON>, Card, CardContent, Grid, Box,
    AppBar, Toolbar, IconButton, Avatar, Menu, MenuItem,
    Divider, ListItemIcon
} from '@mui/material';
import {
    LocalAtmRounded,
    CarRepairRounded,
    ShieldRounded,
    <PERSON>u as MenuIcon,
    Logout as LogoutIcon,
    AccountCircle,
    Settings
} from '@mui/icons-material';
import { logout } from '../Services/authservice'; // Correct named import
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { useState } from 'react';

const AnimatedCard = motion(Card);

export default function DriverDashboard() {
    const navigate = useNavigate();
    const [anchorEl, setAnchorEl] = useState(null);
    const open = Boolean(anchorEl);

    const handleCardClick = (path) => navigate(path);
    const handleMenuOpen = (event) => setAnchorEl(event.currentTarget);
    const handleMenuClose = () => setAnchorEl(null);

    // Corrected single logout handler
    const handleLogout = async () => {
        try {
            await logout(); // API call to invalidate session
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            navigate('/signin'); // Redirect to login page
        } catch (error) {
            console.error('Logout error:', error);
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            navigate('/signin'); // Redirect to login page on error
        }
    };

    const cardVariants = {
        hover: { y: -5, boxShadow: '0px 8px 24px rgba(0, 0, 0, 0.12)' },
        tap: { scale: 0.98 }
    };

    const cards = [
        {
            title: "Apply for Tax Sticker",
            description: "Submit your request to apply for or renew your vehicle tax sticker.",
            icon: <LocalAtmRounded color="primary" sx={{ fontSize: 48, mb: 1 }} />,
            path: "/driver/tax-sticker",
            color: "primary.main"
        },
        {
            title: "Schedule Technical Checkup",
            description: "Book an appointment for your vehicle's technical inspection.",
            icon: <CarRepairRounded color="secondary" sx={{ fontSize: 48, mb: 1 }} />,
            path: "/driver/technical-checkup",
            color: "secondary.main"
        },
        {
            title: "Manage Insurance",
            description: "Review or update your current insurance details and coverage.",
            icon: <ShieldRounded color="success" sx={{ fontSize: 48, mb: 1 }} />,
            path: "/driver/insurance",
            color: "success.main"
        }
    ];

    return (
        <Box sx={{ display: 'flex', flexDirection: 'column', minHeight: '100vh' }}>
            {/* Navigation Bar */}
            <AppBar position="static" elevation={0} sx={{ bgcolor: 'background.paper' }}>
                <Toolbar sx={{ justifyContent: 'space-between' }}>
                    <IconButton edge="start" color="inherit" aria-label="menu">
                        <MenuIcon />
                    </IconButton>

                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <IconButton
                            onClick={handleMenuOpen}
                            size="small"
                            sx={{ ml: 2 }}
                            aria-controls={open ? 'account-menu' : undefined}
                            aria-haspopup="true"
                            aria-expanded={open ? 'true' : undefined}
                        >
                            <Avatar sx={{ width: 32, height: 32 }}>D</Avatar>
                        </IconButton>
                    </Box>
                </Toolbar>
            </AppBar>

            {/* Profile Dropdown Menu */}
            <Menu
                anchorEl={anchorEl}
                id="account-menu"
                open={open}
                onClose={handleMenuClose}
                onClick={handleMenuClose}
                PaperProps={{
                    elevation: 0,
                    sx: {
                        overflow: 'visible',
                        filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.32))',
                        mt: 1.5,
                        '& .MuiAvatar-root': {
                            width: 32,
                            height: 32,
                            ml: -0.5,
                            mr: 1,
                        },
                        '&:before': {
                            content: '""',
                            display: 'block',
                            position: 'absolute',
                            top: 0,
                            right: 14,
                            width: 10,
                            height: 10,
                            bgcolor: 'background.paper',
                            transform: 'translateY(-50%) rotate(45deg)',
                            zIndex: 0,
                        },
                    },
                }}
                transformOrigin={{ horizontal: 'right', vertical: 'top' }}
                anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
            >
                <MenuItem onClick={() => navigate('/driver/profile')}>
                    <Avatar /> My Profile
                </MenuItem>
                <MenuItem onClick={() => navigate('/driver/settings')}>
                    <ListItemIcon>
                        <Settings fontSize="small" />
                    </ListItemIcon>
                    Settings
                </MenuItem>
                <Divider />
                <MenuItem onClick={handleLogout}>
                    <ListItemIcon>
                        <LogoutIcon fontSize="small" />
                    </ListItemIcon>
                    Logout
                </MenuItem>
            </Menu>

            {/* Dashboard Content */}
            <Container maxWidth="lg" sx={{
                mt: { xs: 3, md: 5 },
                mb: 10,
                py: 3,
                flex: 1
            }}>
                <Box textAlign="center" mb={4}>
                    <Typography
                        variant="h4"
                        component="h1"
                        gutterBottom
                        sx={{
                            fontWeight: 700,
                            color: 'text.primary',
                            mb: 1
                        }}
                    >
                        Driver Dashboard
                    </Typography>
                    <Typography
                        variant="subtitle1"
                        color="text.secondary"
                        sx={{ maxWidth: 600, mx: 'auto' }}
                    >
                        Manage all your vehicle administrative tasks in one place
                    </Typography>
                </Box>

                <Grid container spacing={3} justifyContent="center">
                    {cards.map((card, index) => (
                        <Grid item xs={12} sm={6} md={4} key={index}>
                            <AnimatedCard
                                onClick={() => handleCardClick(card.path)}
                                variants={cardVariants}
                                whileHover="hover"
                                whileTap="tap"
                                sx={{
                                    height: '100%',
                                    display: 'flex',
                                    flexDirection: 'column',
                                    borderRadius: 3,
                                    cursor: 'pointer',
                                    borderLeft: `4px solid`,
                                    borderColor: card.color,
                                    transition: 'all 0.3s ease',
                                    '&:hover': {
                                        borderLeftWidth: 8,
                                    }
                                }}
                            >
                                <CardContent sx={{
                                    flexGrow: 1,
                                    display: 'flex',
                                    flexDirection: 'column',
                                    alignItems: 'center',
                                    textAlign: 'center',
                                    p: 3
                                }}>
                                    {card.icon}
                                    <Typography
                                        variant="h6"
                                        component="h2"
                                        gutterBottom
                                        sx={{ fontWeight: 600 }}
                                    >
                                        {card.title}
                                    </Typography>
                                    <Typography
                                        variant="body2"
                                        color="text.secondary"
                                        sx={{ mb: 2 }}
                                    >
                                        {card.description}
                                    </Typography>
                                    <Typography
                                        variant="caption"
                                        color={card.color}
                                        sx={{ mt: 'auto', fontWeight: 500 }}
                                    >
                                        Click to proceed →
                                    </Typography>
                                </CardContent>
                            </AnimatedCard>
                        </Grid>
                    ))}
                </Grid>
            </Container>
        </Box>
    );
}