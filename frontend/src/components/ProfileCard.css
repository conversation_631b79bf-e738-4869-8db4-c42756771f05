/* Profile Card Styles */
.profile-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.profile-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.profile-card.compact {
  max-width: 300px;
}

.profile-card-header {
  display: flex;
  align-items: center;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.profile-card.compact .profile-card-header {
  padding: 16px;
}

.profile-card-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: bold;
  margin-right: 16px;
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.profile-card.compact .profile-card-avatar {
  width: 50px;
  height: 50px;
  font-size: 16px;
  margin-right: 12px;
}

.profile-card-info {
  flex: 1;
}

.profile-card-name {
  margin: 0 0 4px 0;
  font-size: 18px;
  font-weight: 600;
}

.profile-card.compact .profile-card-name {
  font-size: 16px;
}

.profile-card-email {
  margin: 0 0 8px 0;
  font-size: 14px;
  opacity: 0.9;
}

.profile-card.compact .profile-card-email {
  font-size: 12px;
  margin-bottom: 0;
}

.profile-card-badges {
  display: flex;
  gap: 8px;
}

.profile-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.profile-badge.verified {
  background: rgba(40, 167, 69, 0.2);
  color: #28a745;
  border: 1px solid rgba(40, 167, 69, 0.3);
}

.profile-badge.unverified {
  background: rgba(255, 193, 7, 0.2);
  color: #ffc107;
  border: 1px solid rgba(255, 193, 7, 0.3);
}

.profile-card-stats {
  padding: 20px;
  background: #f8f9fa;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #e9ecef;
}

.stat-item:last-child {
  border-bottom: none;
}

.stat-label {
  font-size: 12px;
  color: #6c757d;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-value {
  font-size: 14px;
  color: #495057;
  font-weight: 500;
}

.profile-card-loading, .profile-card-error {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #6c757d;
  gap: 12px;
}

.loading-spinner-small {
  width: 20px;
  height: 20px;
  border: 2px solid #e9ecef;
  border-top: 2px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.profile-card-error {
  color: #dc3545;
}

/* Responsive Design */
@media (max-width: 768px) {
  .profile-card-header {
    flex-direction: column;
    text-align: center;
    padding: 16px;
  }

  .profile-card-avatar {
    margin-right: 0;
    margin-bottom: 12px;
  }

  .profile-card-stats {
    padding: 16px;
  }

  .stat-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}
