import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { login } from "../../Services/authservice";
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import TextField from '@mui/material/TextField';
import Typography from '@mui/material/Typography';
import FormControl from '@mui/material/FormControl';
import FormLabel from '@mui/material/FormLabel';
import Divider from '@mui/material/Divider';
import LinkMui from '@mui/material/Link';
import Checkbox from '@mui/material/Checkbox';
import FormControlLabel from '@mui/material/FormControlLabel';
import GoogleIcon from '@mui/icons-material/Google';
import FacebookIcon from '@mui/icons-material/Facebook';
import Alert from '@mui/material/Alert';

export default function Login() {
  const navigate = useNavigate();

  // State for form inputs
  const [formData, setFormData] = useState({
    email: '',
    password: '',
  });

  // State for errors
  const [emailError, setEmailError] = useState(false);
  const [emailErrorMessage, setEmailErrorMessage] = useState('');
  const [passwordError, setPasswordError] = useState(false);
  const [passwordErrorMessage, setPasswordErrorMessage] = useState('');
  const [generalError, setGeneralError] = useState('');

  // State for loading status
  const [isLoading, setIsLoading] = useState(false);

  // Clear all errors
  const clearErrors = () => {
    setEmailError(false);
    setEmailErrorMessage('');
    setPasswordError(false);
    setPasswordErrorMessage('');
    setGeneralError('');
  };

  // Handle input changes with dynamic validation
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
    
    // Clear errors when user starts typing
    clearErrors();
    
    validateField(name, value);
  };

  // Validate individual field
  const validateField = (name, value) => {
    let isValid = true;

    switch (name) {
      case 'email':
        if (!value.trim()) {
          setEmailError(true);
          setEmailErrorMessage('Email is required');
          isValid = false;
        } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
          setEmailError(true);
          setEmailErrorMessage('Please enter a valid email address.');
          isValid = false;
        } else {
          setEmailError(false);
          setEmailErrorMessage('');
        }
        break;
      case 'password':
        if (!value || value.length < 6) {
          setPasswordError(true);
          setPasswordErrorMessage('Password must be at least 6 characters.');
          isValid = false;
        } else {
          setPasswordError(false);
          setPasswordErrorMessage('');
        }
        break;
      default:
        break;
    }

    return isValid;
  };

  // Validate entire form before submission
  const validateForm = () => {
    const isEmailValid = validateField('email', formData.email);
    const isPasswordValid = validateField('password', formData.password);
    return isEmailValid && isPasswordValid;
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (isLoading) return;

    const isValid = validateForm();
    if (!isValid) return;

    setIsLoading(true);
    clearErrors();
    
    // Clear any existing tokens before attempting login
    console.log('Clearing existing tokens...');
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    
    try {
      console.log('Attempting login with:', { email: formData.email });
      const response = await login(formData);
      console.log('Login successful:', response);
      
      // Verify token was stored
      const storedToken = localStorage.getItem('token');
      console.log('Token stored after login:', storedToken ? 'Yes' : 'No');
      
      navigate('/driver');
    } catch (error) {
      console.error("Login error:", error);
      
      // Set appropriate error messages based on the error
      if (error.message.includes('401') || error.message.includes('Unauthorized')) {
        setGeneralError('Invalid email or password. Please check your credentials and try again.');
      } else if (error.message.includes('403') || error.message.includes('Forbidden')) {
        setGeneralError('Your account is locked or disabled. Please contact support.');
      } else if (error.message.includes('500')) {
        setGeneralError('Server error. Please try again later.');
      } else if (error.message.includes('Network')) {
        setGeneralError('Network error. Please check your connection and try again.');
      } else {
        setGeneralError('Login failed. Please try again.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Box
      component="form"
      onSubmit={handleSubmit}
      noValidate
      sx={{
        display: 'flex',
        flexDirection: 'column',
        width: '100%',
        gap: 2,
        maxWidth: '450px',
        margin: 'auto',
        padding: 3,
        boxShadow: 3,
        borderRadius: 2,
        bgcolor: 'background.paper',
      }}
    >
      <Typography component="h1" variant="h5">
        Sign in
      </Typography>

      {generalError && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {generalError}
        </Alert>
      )}

      <FormControl>
        <FormLabel htmlFor="email">Email</FormLabel>
        <TextField
          error={emailError}
          helperText={emailErrorMessage}
          id="email"
          type="email"
          name="email"
          placeholder="<EMAIL>"
          autoComplete="email"
          autoFocus
          required
          fullWidth
          variant="outlined"
          color={emailError ? 'error' : 'primary'}
          value={formData.email}
          onChange={handleInputChange}
          disabled={isLoading}
        />
      </FormControl>

      <FormControl>
        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
          <FormLabel htmlFor="password">Password</FormLabel>
          <LinkMui
            component="button"
            type="button"
            onClick={() => alert('Redirect to forgot password')}
            variant="body2"
            sx={{ alignSelf: 'baseline' }}
          >
            Forgot your password?
          </LinkMui>
        </Box>
        <TextField
          error={passwordError}
          helperText={passwordErrorMessage}
          name="password"
          placeholder="••••••"
          type="password"
          id="password"
          autoComplete="current-password"
          required
          fullWidth
          variant="outlined"
          color={passwordError ? 'error' : 'primary'}
          value={formData.password}
          onChange={handleInputChange}
          disabled={isLoading}
        />
      </FormControl>

      <FormControlLabel
        control={<Checkbox value="remember" color="primary" />}
        label="Remember me"
      />

      <Button
        type="submit"
        fullWidth
        variant="contained"
        disabled={isLoading}
      >
        {isLoading ? 'Signing in...' : 'Sign in'}
      </Button>

      <Typography sx={{ textAlign: 'center' }}>
        Don&apos;t have an account?{' '}
        <LinkMui
          href="/signup"
          variant="body2"
          sx={{ alignSelf: 'center' }}
        >
          Sign up
        </LinkMui>
      </Typography>

      <Divider>or</Divider>

      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
        <Button
          fullWidth
          variant="outlined"
          onClick={() => alert('Sign in with Google')}
          startIcon={<GoogleIcon />}
        >
          Sign in with Google
        </Button>
        <Button
          fullWidth
          variant="outlined"
          onClick={() => alert('Sign in with Facebook')}
          startIcon={<FacebookIcon />}
        >
          Sign in with Facebook
        </Button>
      </Box>
    </Box>
  );
}
