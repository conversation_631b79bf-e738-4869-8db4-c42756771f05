/* Navigation Styles */
.navigation {
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  height: 64px;
}

/* Logo */
.nav-logo {
  font-size: 20px;
  font-weight: bold;
}

.logo-link {
  text-decoration: none;
  color: #007bff;
  display: flex;
  align-items: center;
  gap: 8px;
}

.logo-link:hover {
  color: #0056b3;
}

/* Navigation Links */
.nav-links {
  display: flex;
  align-items: center;
  gap: 24px;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
  color: #6c757d;
  font-weight: 500;
  padding: 8px 16px;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.nav-link:hover {
  color: #007bff;
  background: #f8f9fa;
}

.nav-icon {
  font-size: 16px;
}

/* Profile Section */
.nav-profile {
  position: relative;
}

.profile-trigger {
  display: flex;
  align-items: center;
  gap: 8px;
  background: none;
  border: 1px solid #e9ecef;
  padding: 8px 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.profile-trigger:hover {
  background: #f8f9fa;
  border-color: #dee2e6;
}

.profile-avatar-small {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: white;
}

.dropdown-arrow {
  font-size: 10px;
  color: #6c757d;
  transition: transform 0.2s ease;
}

.profile-trigger:hover .dropdown-arrow {
  transform: rotate(180deg);
}

/* Profile Dropdown */
.profile-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 8px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  min-width: 280px;
  overflow: hidden;
  z-index: 1000;
  border: 1px solid #e9ecef;
}

.dropdown-header {
  padding: 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.dropdown-menu {
  padding: 8px 0;
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
  padding: 12px 16px;
  text-decoration: none;
  color: #495057;
  background: none;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s ease;
  font-size: 14px;
}

.dropdown-item:hover {
  background: #f8f9fa;
}

.dropdown-icon {
  font-size: 16px;
  width: 20px;
  text-align: center;
}

.dropdown-divider {
  height: 1px;
  background: #e9ecef;
  margin: 8px 0;
}

.logout-item {
  color: #dc3545;
}

.logout-item:hover {
  background: #f8d7da;
}

.dropdown-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav-container {
    padding: 0 16px;
  }

  .nav-links {
    display: none; /* Hide navigation links on mobile */
  }

  .profile-dropdown {
    right: -16px;
    left: 16px;
    min-width: auto;
  }
}

@media (max-width: 480px) {
  .nav-container {
    height: 56px;
  }

  .logo-link {
    font-size: 18px;
  }

  .profile-dropdown {
    right: -16px;
    left: 16px;
  }
}
