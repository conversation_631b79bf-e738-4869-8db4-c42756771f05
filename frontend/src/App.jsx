import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import Login from './components/LoginUpdated'; // Use the updated login component
import Register from './components/Register';
import TokenDebugger from './components/TokenDebugger';
import LoginDebugger from './components/LoginDebugger';
import TokenTest from './components/TokenTest';
import DriverTest from './components/DriverTest';
import DriverOnlyPage from './components/DriverOnlyPage';
import Unauthorized from './components/Unauthorized';
import RoleProtectedRoute from './components/RoleProtectedRoute';
import ProfileDashboard from './components/ProfileDashboard';
import { isAuthenticated } from './Services/authservice';

// Layout component for authenticated pages
const AuthenticatedLayout = ({ children }) => {
  return (
    <div className="app-layout">
      <Navigation />
      <main className="main-content">
        {children}
      </main>
    </div>
  );
};

// Layout component for public pages (no navigation)
const PublicLayout = ({ children }) => {
  return (
    <div className="public-layout">
      {children}
    </div>
  );
};

function App() {
  return (
    <Router>
      <Routes>
        {/* Public routes (no navigation) */}
        <Route path="/login" element={<PublicLayout><Login /></PublicLayout>} />
        <Route path="/register" element={<PublicLayout><Register /></PublicLayout>} />
        <Route path="/unauthorized" element={<PublicLayout><Unauthorized /></PublicLayout>} />

        {/* Debug routes (no navigation) */}
        <Route path="/token-debug" element={<PublicLayout><TokenDebugger /></PublicLayout>} />
        <Route path="/login-debug" element={<PublicLayout><LoginDebugger /></PublicLayout>} />
        <Route path="/token-test" element={<PublicLayout><TokenTest /></PublicLayout>} />

        {/* Protected routes (with navigation) */}
        <Route
          path="/dashboard"
          element={
            isAuthenticated() ? (
              <AuthenticatedLayout>
                <div style={{ padding: '20px' }}>
                  <h1>Dashboard</h1>
                  <p>Welcome to your dashboard! This is accessible to all authenticated users.</p>
                </div>
              </AuthenticatedLayout>
            ) : (
              <Navigate to="/login" replace />
            )
          }
        />

        <Route
          path="/profile"
          element={
            isAuthenticated() ? (
              <ProfileDashboard />
            ) : (
              <Navigate to="/login" replace />
            )
          }
        />

        <Route
          path="/vehicles"
          element={
            isAuthenticated() ? (
              <AuthenticatedLayout>
                <div style={{ padding: '20px' }}>
                  <h1>Vehicle Management</h1>
                  <p>Manage your vehicles here.</p>
                </div>
              </AuthenticatedLayout>
            ) : (
              <Navigate to="/login" replace />
            )
          }
        />

        {/* Role-protected routes (with navigation) */}
        <Route
          path="/driver-dashboard"
          element={
            <RoleProtectedRoute requiredRole="DRIVER">
              <AuthenticatedLayout>
                <DriverOnlyPage />
              </AuthenticatedLayout>
            </RoleProtectedRoute>
          }
        />

        <Route
          path="/driver-test"
          element={
            isAuthenticated() ? (
              <AuthenticatedLayout>
                <DriverTest />
              </AuthenticatedLayout>
            ) : (
              <Navigate to="/login" replace />
            )
          }
        />

        {/* Default route */}
        <Route path="/" element={
          isAuthenticated() ? (
            <Navigate to="/dashboard" replace />
          ) : (
            <Navigate to="/login" replace />
          )
        } />
      </Routes>
    </Router>
  );
}

export default App;
