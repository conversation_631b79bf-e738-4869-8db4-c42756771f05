import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import Login from './components/LoginUpdated'; // Use the updated login component
import Register from './components/Register';
import TokenDebugger from './components/TokenDebugger';
import LoginDebugger from './components/LoginDebugger';
import TokenTest from './components/TokenTest';
import DriverTest from './components/DriverTest';
import DriverOnlyPage from './components/DriverOnlyPage';
import Unauthorized from './components/Unauthorized';
import RoleProtectedRoute from './components/RoleProtectedRoute';
import { isAuthenticated } from './Services/authservice';

function App() {
  return (
    <Router>
      <Routes>
        {/* Public routes */}
        <Route path="/login" element={<Login />} />
        <Route path="/register" element={<Register />} />
        <Route path="/unauthorized" element={<Unauthorized />} />
        <Route path="/token-debug" element={<TokenDebugger />} />
        <Route path="/login-debug" element={<LoginDebugger />} />
        <Route path="/token-test" element={<TokenTest />} />

        {/* Protected routes (require authentication) */}
        <Route
          path="/dashboard"
          element={
            isAuthenticated() ? (
              <div>Dashboard (Accessible to all authenticated users)</div>
            ) : (
              <Navigate to="/login" replace />
            )
          }
        />

        {/* Role-protected routes */}
        <Route
          path="/driver-dashboard"
          element={
            <RoleProtectedRoute requiredRole="DRIVER">
              <DriverOnlyPage />
            </RoleProtectedRoute>
          }
        />

        {/* Default route */}
        <Route path="/" element={<Navigate to="/login" replace />} />
      </Routes>
    </Router>
  );
}

export default App;
