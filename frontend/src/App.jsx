import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import Login from './components/Login';
import Register from './components/Register';
import TokenDebugger from './components/TokenDebugger';
import DriverOnlyPage from './components/DriverOnlyPage';
import Unauthorized from './components/Unauthorized';
import RoleProtectedRoute from './components/RoleProtectedRoute';
import { isAuthenticated } from './services/authService';

function App() {
  return (
    <Router>
      <Routes>
        {/* Public routes */}
        <Route path="/login" element={<Login />} />
        <Route path="/register" element={<Register />} />
        <Route path="/unauthorized" element={<Unauthorized />} />
        <Route path="/token-debug" element={<TokenDebugger />} />
        
        {/* Protected routes (require authentication) */}
        <Route 
          path="/dashboard" 
          element={
            isAuthenticated() ? (
              <div>Dashboard (Accessible to all authenticated users)</div>
            ) : (
              <Navigate to="/login" replace />
            )
          } 
        />
        
        {/* Role-protected routes */}
        <Route 
          path="/driver-dashboard" 
          element={
            <RoleProtectedRoute requiredRole="DRIVER">
              <DriverOnlyPage />
            </RoleProtectedRoute>
          } 
        />
        
        {/* Default route */}
        <Route path="/" element={<Navigate to="/login" replace />} />
      </Routes>
    </Router>
  );
}

export default App;
