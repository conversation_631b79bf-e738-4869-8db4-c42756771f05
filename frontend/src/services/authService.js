// API URL
const API_URL = 'http://localhost:8082/api/v1/auth';

// Register function
export const register = async (userData) => {
  try {
    const response = await fetch(`${API_URL}/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include', // Important for CORS with credentials
      body: JSON.stringify(userData),
    });

    if (!response.ok) {
      throw new Error(`Registration failed: ${response.status}`);
    }

    const data = await response.json();

    // Store the token in localStorage
    if (data.accessToken) {
      localStorage.setItem('token', data.accessToken);

      // Import and use the jwt utils to extract and store user info
      // Import the functions directly to avoid require issues
      import('../utils/jwtUtils').then(({ decodeToken, getUserRole }) => {
        const decodedToken = decodeToken(data.accessToken);
        if (decodedToken) {
          // Get the role using the updated function
          const role = getUserRole();

          const userInfo = {
            username: decodedToken.sub,
            authorities: decodedToken.authorities || [],
            role: role
          };
          localStorage.setItem('user', JSON.stringify(userInfo));
        }
      }).catch(err => console.error('Error importing jwtUtils:', err));
    }

    return data;
  } catch (error) {
    console.error('Registration error:', error);
    throw error;
  }
};

// Login function
export const login = async (credentials) => {
  try {
    const response = await fetch(`${API_URL}/authenticate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include', // Important for CORS with credentials
      body: JSON.stringify({
        email: credentials.email,
        password: credentials.password,
      }),
    });

    if (!response.ok) {
      throw new Error(`Login failed: ${response.status}`);
    }

    const data = await response.json();

    // Store the token in localStorage
    if (data.accessToken) {
      localStorage.setItem('token', data.accessToken);

      // Import and use the jwt utils to extract and store user info
      // Import the functions directly to avoid require issues
      import('../utils/jwtUtils').then(({ decodeToken, getUserRole }) => {
        const decodedToken = decodeToken(data.accessToken);
        if (decodedToken) {
          // Get the role using the updated function
          const role = getUserRole();

          const userInfo = {
            username: decodedToken.sub,
            authorities: decodedToken.authorities || [],
            role: role
          };
          localStorage.setItem('user', JSON.stringify(userInfo));
        }
      }).catch(err => console.error('Error importing jwtUtils:', err));
    }

    return data;
  } catch (error) {
    console.error('Login error:', error);
    throw error;
  }
};

// Logout function
export const logout = async () => {
  try {
    const token = localStorage.getItem('token');

    if (!token) {
      // If there's no token, just return (user is already logged out)
      return;
    }

    const response = await fetch(`${API_URL}/logout`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      credentials: 'include', // Important for CORS with credentials
    });

    if (!response.ok) {
      throw new Error(`Logout failed: ${response.status}`);
    }

    // Clear the token and user info from localStorage
    localStorage.removeItem('token');
    localStorage.removeItem('user');

    return await response.text();
  } catch (error) {
    console.error('Logout error:', error);
    // Even if the API call fails, we should still clear the token and user info
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    throw error;
  }
};

// Function to get the current user's token
export const getToken = () => {
  return localStorage.getItem('token');
};

// Function to check if user is authenticated
export const isAuthenticated = () => {
  return !!getToken();
};

// Function to get the current user
export const getCurrentUser = () => {
  const userStr = localStorage.getItem('user');
  if (!userStr) return null;

  try {
    return JSON.parse(userStr);
  } catch (error) {
    console.error('Error parsing user info:', error);
    return null;
  }
};

// Function to get the user's role
export const getUserRole = () => {
  const user = getCurrentUser();
  return user ? user.role : null;
};

// Function to check if user has a specific role
export const hasRole = (role) => {
  const userRole = getUserRole();
  return userRole === role;
};

// Function to refresh the token
export const refreshToken = async () => {
  try {
    const token = localStorage.getItem('token');

    if (!token) {
      throw new Error('No token found');
    }

    const response = await fetch(`${API_URL}/refresh-token`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      credentials: 'include', // Important for CORS with credentials
    });

    if (!response.ok) {
      throw new Error(`Token refresh failed: ${response.status}`);
    }

    const data = await response.json();

    // Update the token in localStorage
    if (data.accessToken) {
      localStorage.setItem('token', data.accessToken);
    }

    return data;
  } catch (error) {
    console.error('Token refresh error:', error);
    throw error;
  }
};
