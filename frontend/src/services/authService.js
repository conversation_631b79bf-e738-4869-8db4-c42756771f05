// API URL - Now using API Gateway
const API_URL = 'http://localhost:8080/api/v1/auth';

// Register function
export const register = async (userData) => {
  try {
    const response = await fetch(`${API_URL}/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include', // Important for CORS with credentials
      body: JSON.stringify(userData),
    });

    if (!response.ok) {
      throw new Error(`Registration failed: ${response.status}`);
    }

    const data = await response.json();

    // Store the token in localStorage
    if (data.accessToken) {
      console.log('Storing token:', data.accessToken.substring(0, 20) + '...');
      localStorage.setItem('token', data.accessToken);

      // Decode and store user info
      try {
        // Simple base64 decode for JWT payload
        const payload = JSON.parse(atob(data.accessToken.split('.')[1]));
        console.log('Decoded token payload:', payload);

        const userInfo = {
          username: payload.sub,
          authorities: payload.authorities || [],
          role: payload.authorities && payload.authorities.length > 0 ? payload.authorities[0] : null
        };

        console.log('Storing user info:', userInfo);
        localStorage.setItem('user', JSON.stringify(userInfo));
      } catch (err) {
        console.error('Error decoding token:', err);
      }
    } else {
      console.error('No accessToken in response:', data);
    }

    return data;
  } catch (error) {
    console.error('Registration error:', error);
    throw error;
  }
};

// Login function
export const login = async (credentials) => {
  try {
    console.log('🔄 Starting login request...');
    console.log('📧 Email:', credentials.email);
    console.log('🌐 API URL:', `${API_URL}/authenticate`);

    const response = await fetch(`${API_URL}/authenticate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include', // Important for CORS with credentials
      body: JSON.stringify({
        email: credentials.email,
        password: credentials.password,
      }),
    });

    console.log('📡 Response status:', response.status);
    console.log('📡 Response headers:', Object.fromEntries(response.headers.entries()));

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ Login failed:', errorText);
      throw new Error(`Login failed: ${response.status} - ${errorText}`);
    }

    const data = await response.json();
    console.log('📦 Full response data:', data);
    console.log('🔑 Available keys in response:', Object.keys(data));

    // Check for different possible token property names
    const tokenKey = data.accessToken ? 'accessToken' :
                     data.access_token ? 'access_token' :
                     data.token ? 'token' :
                     data.jwt ? 'jwt' : null;

    console.log('🔍 Token key found:', tokenKey);

    if (tokenKey && data[tokenKey]) {
      const token = data[tokenKey];
      console.log('✅ Token found with key:', tokenKey);
      console.log('🔑 Storing token:', token.substring(0, 20) + '...');
      localStorage.setItem('token', token);

      // Decode and store user info
      try {
        // Simple base64 decode for JWT payload
        const payload = JSON.parse(atob(token.split('.')[1]));
        console.log('📋 Decoded token payload:', payload);

        const userInfo = {
          username: payload.sub,
          authorities: payload.authorities || [],
          role: payload.authorities && payload.authorities.length > 0 ? payload.authorities[0] : null
        };

        console.log('👤 Storing user info:', userInfo);
        localStorage.setItem('user', JSON.stringify(userInfo));

        // Verify storage
        const storedToken = localStorage.getItem('token');
        const storedUser = localStorage.getItem('user');
        console.log('✅ Token stored successfully:', !!storedToken);
        console.log('✅ User info stored successfully:', !!storedUser);

      } catch (err) {
        console.error('❌ Error decoding token:', err);
      }
    } else {
      console.error('❌ No token found in response. Available keys:', Object.keys(data));
      console.error('❌ Full response:', data);
    }

    return data;
  } catch (error) {
    console.error('❌ Login error:', error);
    throw error;
  }
};

// Logout function
export const logout = async () => {
  try {
    const token = localStorage.getItem('token');

    if (!token) {
      // If there's no token, just return (user is already logged out)
      return;
    }

    const response = await fetch(`${API_URL}/logout`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      credentials: 'include', // Important for CORS with credentials
    });

    if (!response.ok) {
      throw new Error(`Logout failed: ${response.status}`);
    }

    // Clear the token and user info from localStorage
    localStorage.removeItem('token');
    localStorage.removeItem('user');

    return await response.text();
  } catch (error) {
    console.error('Logout error:', error);
    // Even if the API call fails, we should still clear the token and user info
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    throw error;
  }
};
// Function to get the current user's token
export const getToken = () => {
  return localStorage.getItem('token');
};

// Function to check if user is authenticated
export const isAuthenticated = () => {
  return !!getToken();
};

// Function to refresh the token
export const refreshToken = async () => {
  try {
    const token = localStorage.getItem('token');

    if (!token) {
      throw new Error('No token found');
    }

    const response = await fetch(`${API_URL}/refresh-token`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      credentials: 'include', // Important for CORS with credentials
    });

    if (!response.ok) {
      throw new Error(`Token refresh failed: ${response.status}`);
    }

    const data = await response.json();

    // Update the token in localStorage
    if (data.accessToken) {
      localStorage.setItem('token', data.accessToken);
    }

    return data;
  } catch (error) {
    console.error('Token refresh error:', error);
    throw error;
  }







};


// Get user role from localStorage or decoded token
export const getUserRole = () => {
  const user = JSON.parse(localStorage.getItem('user'));
  console.log(user?.role);
  return user?.role || null;

};