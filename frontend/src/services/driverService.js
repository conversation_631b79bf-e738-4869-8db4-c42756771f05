// API URL - Now using API Gateway
const API_URL = 'http://localhost:8080';

// Helper function to get authorization headers
const getAuthHeaders = () => {
  const token = localStorage.getItem('token');
  if (!token) {
    throw new Error('No authentication token found. Please log in.');
  }

  return {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json',
  };
};

// Helper function to handle API responses
const handleResponse = async (response) => {
  if (!response.ok) {
    if (response.status === 401) {
      // Token might be expired, redirect to login
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      window.location.href = '/login';
      throw new Error('Authentication expired. Please log in again.');
    } else if (response.status === 403) {
      throw new Error('Access denied. You do not have permission to access this resource.');
    } else {
      const errorText = await response.text();
      throw new Error(`Request failed: ${response.status} - ${errorText}`);
    }
  }

  return response.json();
};

// Get all drivers
export const getDrivers = async () => {
  try {
    console.log('🚗 Fetching drivers...');

    const response = await fetch(`${API_URL}/api/v1/driverprofile/getDrivers`, {
      method: 'GET',
      headers: getAuthHeaders(),
      credentials: 'include',
    });

    const data = await handleResponse(response);
    console.log('✅ Drivers fetched successfully:', data);
    return data;
  } catch (error) {
    console.error('❌ Error fetching drivers:', error);
    throw error;
  }
};

// Get driver by ID
export const getDriverById = async (id) => {
  try {
    console.log(`🚗 Fetching driver with ID: ${id}`);

    const response = await fetch(`${API_URL}/getDriver/${id}`, {
      method: 'GET',
      headers: getAuthHeaders(),
      credentials: 'include',
    });

    const data = await handleResponse(response);
    console.log('✅ Driver fetched successfully:', data);
    return data;
  } catch (error) {
    console.error('❌ Error fetching driver:', error);
    throw error;
  }
};

// Create a new driver
export const createDriver = async (driverData) => {
  try {
    console.log('🚗 Creating new driver:', driverData);

    const response = await fetch(`${API_URL}/createDriver`, {
      method: 'POST',
      headers: getAuthHeaders(),
      credentials: 'include',
      body: JSON.stringify(driverData),
    });

    const data = await handleResponse(response);
    console.log('✅ Driver created successfully:', data);
    return data;
  } catch (error) {
    console.error('❌ Error creating driver:', error);
    throw error;
  }
};

// Update driver
export const updateDriver = async (id, driverData) => {
  try {
    console.log(`🚗 Updating driver with ID: ${id}`, driverData);

    const response = await fetch(`${API_URL}/updateDriver/${id}`, {
      method: 'PUT',
      headers: getAuthHeaders(),
      credentials: 'include',
      body: JSON.stringify(driverData),
    });

    const data = await handleResponse(response);
    console.log('✅ Driver updated successfully:', data);
    return data;
  } catch (error) {
    console.error('❌ Error updating driver:', error);
    throw error;
  }
};

// Delete driver
export const deleteDriver = async (id) => {
  try {
    console.log(`🚗 Deleting driver with ID: ${id}`);

    const response = await fetch(`${API_URL}/deleteDriver/${id}`, {
      method: 'DELETE',
      headers: getAuthHeaders(),
      credentials: 'include',
    });

    if (response.status === 204) {
      console.log('✅ Driver deleted successfully');
      return { message: 'Driver deleted successfully' };
    }

    const data = await handleResponse(response);
    console.log('✅ Driver deleted successfully:', data);
    return data;
  } catch (error) {
    console.error('❌ Error deleting driver:', error);
    throw error;
  }
};
