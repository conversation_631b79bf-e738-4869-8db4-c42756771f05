// API URL
const API_URL = 'http://localhost:8082';

// Helper function to get authorization headers
const getAuthHeaders = () => {
  const token = localStorage.getItem('token');
  if (!token) {
    throw new Error('No authentication token found. Please log in.');
  }

  return {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json',
  };
};

// Helper function to handle API responses
const handleResponse = async (response) => {
  if (!response.ok) {
    if (response.status === 401) {
      // Token might be expired, redirect to login
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      window.location.href = '/login';
      throw new Error('Authentication expired. Please log in again.');
    } else if (response.status === 403) {
      throw new Error('Access denied. You do not have permission to access this resource.');
    } else {
      const errorText = await response.text();
      let errorMessage;
      try {
        const errorJson = JSON.parse(errorText);
        errorMessage = errorJson.error || errorJson.message || `Request failed: ${response.status}`;
      } catch {
        errorMessage = `Request failed: ${response.status} - ${errorText}`;
      }
      throw new Error(errorMessage);
    }
  }

  const contentType = response.headers.get('content-type');
  if (contentType && contentType.includes('application/json')) {
    return response.json();
  } else {
    return response.text();
  }
};

// =============================================================================
// PROFILE MANAGEMENT
// =============================================================================

/**
 * Get current user's profile
 */
export const getCurrentProfile = async () => {
  try {
    console.log('👤 Fetching current profile...');
    
    const response = await fetch(`${API_URL}/api/v1/driverprofile/profile`, {
      method: 'GET',
      headers: getAuthHeaders(),
      credentials: 'include',
    });
    
    const data = await handleResponse(response);
    console.log('✅ Profile fetched successfully:', data);
    return data;
  } catch (error) {
    console.error('❌ Error fetching profile:', error);
    throw error;
  }
};

/**
 * Update complete profile
 */
export const updateProfile = async (profileData) => {
  try {
    console.log('👤 Updating complete profile...', profileData);
    
    const response = await fetch(`${API_URL}/api/v1/driverprofile/profile`, {
      method: 'PUT',
      headers: getAuthHeaders(),
      credentials: 'include',
      body: JSON.stringify(profileData),
    });
    
    const data = await handleResponse(response);
    console.log('✅ Profile updated successfully:', data);
    return data;
  } catch (error) {
    console.error('❌ Error updating profile:', error);
    throw error;
  }
};

/**
 * Update username
 */
export const updateUsername = async (newUsername, currentPassword) => {
  try {
    console.log('👤 Updating username...');
    
    const response = await fetch(`${API_URL}/api/v1/driverprofile/username`, {
      method: 'PATCH',
      headers: getAuthHeaders(),
      credentials: 'include',
      body: JSON.stringify({
        newUsername,
        currentPassword
      }),
    });
    
    const data = await handleResponse(response);
    console.log('✅ Username updated successfully:', data);
    
    // Update localStorage user info
    const user = JSON.parse(localStorage.getItem('user') || '{}');
    user.username = data.newUsername;
    localStorage.setItem('user', JSON.stringify(user));
    
    return data;
  } catch (error) {
    console.error('❌ Error updating username:', error);
    throw error;
  }
};

/**
 * Update personal information
 */
export const updatePersonalInfo = async (personalInfo) => {
  try {
    console.log('👤 Updating personal information...', personalInfo);
    
    const response = await fetch(`${API_URL}/api/v1/driverprofile/personal-info`, {
      method: 'PATCH',
      headers: getAuthHeaders(),
      credentials: 'include',
      body: JSON.stringify(personalInfo),
    });
    
    const data = await handleResponse(response);
    console.log('✅ Personal information updated successfully:', data);
    return data;
  } catch (error) {
    console.error('❌ Error updating personal information:', error);
    throw error;
  }
};

/**
 * Update email address
 */
export const updateEmail = async (newEmail, currentPassword) => {
  try {
    console.log('👤 Updating email address...');
    
    const response = await fetch(`${API_URL}/api/v1/driverprofile/email`, {
      method: 'PATCH',
      headers: getAuthHeaders(),
      credentials: 'include',
      body: JSON.stringify({
        newEmail,
        currentPassword
      }),
    });
    
    const data = await handleResponse(response);
    console.log('✅ Email updated successfully:', data);
    
    // Update localStorage user info
    const user = JSON.parse(localStorage.getItem('user') || '{}');
    user.email = data.newEmail;
    localStorage.setItem('user', JSON.stringify(user));
    
    return data;
  } catch (error) {
    console.error('❌ Error updating email:', error);
    throw error;
  }
};

// =============================================================================
// VEHICLE MANAGEMENT
// =============================================================================

/**
 * Get all vehicle registrations
 */
export const getVehicleRegistrations = async () => {
  try {
    console.log('🚗 Fetching vehicle registrations...');
    
    const response = await fetch(`${API_URL}/api/v1/driverprofile/vehicles`, {
      method: 'GET',
      headers: getAuthHeaders(),
      credentials: 'include',
    });
    
    const data = await handleResponse(response);
    console.log('✅ Vehicle registrations fetched successfully:', data);
    return data;
  } catch (error) {
    console.error('❌ Error fetching vehicle registrations:', error);
    throw error;
  }
};

/**
 * Add vehicle registration
 */
export const addVehicleRegistration = async (vehicleRegistration) => {
  try {
    console.log('🚗 Adding vehicle registration:', vehicleRegistration);
    
    const response = await fetch(`${API_URL}/api/v1/driverprofile/vehicles`, {
      method: 'POST',
      headers: getAuthHeaders(),
      credentials: 'include',
      body: JSON.stringify({ vehicleRegistration }),
    });
    
    const data = await handleResponse(response);
    console.log('✅ Vehicle registration added successfully:', data);
    return data;
  } catch (error) {
    console.error('❌ Error adding vehicle registration:', error);
    throw error;
  }
};

/**
 * Remove vehicle registration
 */
export const removeVehicleRegistration = async (vehicleRegistration) => {
  try {
    console.log('🚗 Removing vehicle registration:', vehicleRegistration);
    
    const response = await fetch(`${API_URL}/api/v1/driverprofile/vehicles/${encodeURIComponent(vehicleRegistration)}`, {
      method: 'DELETE',
      headers: getAuthHeaders(),
      credentials: 'include',
    });
    
    const data = await handleResponse(response);
    console.log('✅ Vehicle registration removed successfully:', data);
    return data;
  } catch (error) {
    console.error('❌ Error removing vehicle registration:', error);
    throw error;
  }
};

// =============================================================================
// SECURITY
// =============================================================================

/**
 * Change password
 */
export const changePassword = async (currentPassword, newPassword, confirmationPassword) => {
  try {
    console.log('🔐 Changing password...');
    
    const response = await fetch(`${API_URL}/api/v1/driverprofile/change-password`, {
      method: 'PATCH',
      headers: getAuthHeaders(),
      credentials: 'include',
      body: JSON.stringify({
        currentPassword,
        newPassword,
        confirmationPassword
      }),
    });
    
    const data = await handleResponse(response);
    console.log('✅ Password changed successfully');
    return data;
  } catch (error) {
    console.error('❌ Error changing password:', error);
    throw error;
  }
};

/**
 * Verify current password
 */
export const verifyPassword = async (password) => {
  try {
    console.log('🔐 Verifying password...');
    
    const response = await fetch(`${API_URL}/api/v1/driverprofile/verify-password`, {
      method: 'POST',
      headers: getAuthHeaders(),
      credentials: 'include',
      body: JSON.stringify({ password }),
    });
    
    const data = await handleResponse(response);
    console.log('✅ Password verification result:', data.valid);
    return data.valid;
  } catch (error) {
    console.error('❌ Error verifying password:', error);
    throw error;
  }
};

// =============================================================================
// ACCOUNT MANAGEMENT
// =============================================================================

/**
 * Deactivate account
 */
export const deactivateAccount = async () => {
  try {
    console.log('🏢 Deactivating account...');
    
    const response = await fetch(`${API_URL}/api/v1/driverprofile/deactivate`, {
      method: 'PATCH',
      headers: getAuthHeaders(),
      credentials: 'include',
    });
    
    const data = await handleResponse(response);
    console.log('✅ Account deactivated successfully');
    
    // Clear local storage and redirect to login
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    
    return data;
  } catch (error) {
    console.error('❌ Error deactivating account:', error);
    throw error;
  }
};

// =============================================================================
// VALIDATION
// =============================================================================

/**
 * Check if username is available
 */
export const checkUsernameAvailability = async (username) => {
  try {
    console.log('✅ Checking username availability:', username);
    
    const response = await fetch(`${API_URL}/api/v1/driverprofile/check-username?username=${encodeURIComponent(username)}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
    });
    
    const data = await handleResponse(response);
    console.log('✅ Username availability result:', data.available);
    return data.available;
  } catch (error) {
    console.error('❌ Error checking username availability:', error);
    throw error;
  }
};

/**
 * Check if email is available
 */
export const checkEmailAvailability = async (email) => {
  try {
    console.log('✅ Checking email availability:', email);
    
    const response = await fetch(`${API_URL}/api/v1/driverprofile/check-email?email=${encodeURIComponent(email)}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
    });
    
    const data = await handleResponse(response);
    console.log('✅ Email availability result:', data.available);
    return data.available;
  } catch (error) {
    console.error('❌ Error checking email availability:', error);
    throw error;
  }
};
