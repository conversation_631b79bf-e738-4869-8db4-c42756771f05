// API URL - Using API Gateway
const API_URL = 'http://localhost:8080';

// Helper function to get authorization headers
const getAuthHeaders = () => {
  const token = localStorage.getItem('token');
  if (!token) {
    throw new Error('No authentication token found. Please log in.');
  }

  return {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json',
  };
};

// Helper function to handle API responses
const handleResponse = async (response) => {
  if (!response.ok) {
    if (response.status === 401) {
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      window.location.href = '/login';
      throw new Error('Authentication expired. Please log in again.');
    } else if (response.status === 403) {
      throw new Error('Access denied. You do not have permission to access this resource.');
    } else {
      const errorText = await response.text();
      let errorMessage;
      try {
        const errorJson = JSON.parse(errorText);
        errorMessage = errorJson.error || errorJson.message || `Request failed: ${response.status}`;
      } catch {
        errorMessage = `Request failed: ${response.status} - ${errorText}`;
      }
      throw new Error(errorMessage);
    }
  }

  const contentType = response.headers.get('content-type');
  if (contentType && contentType.includes('application/json')) {
    return response.json();
  } else {
    return response.text();
  }
};

// =============================================================================
// VEHICLE MANAGEMENT FOR TECHNICAL CHECKUP
// =============================================================================

/**
 * Get driver's vehicles for technical checkup
 */
export const getDriverVehicles = async () => {
  try {
    console.log('🚗 Fetching driver vehicles for technical checkup...');
    console.log('🔗 API URL:', `${API_URL}/api/v1/vt/vehicles`);

    const response = await fetch(`${API_URL}/api/v1/vt/vehicles`, {
      method: 'GET',
      headers: getAuthHeaders(),
      credentials: 'include',
    });

    console.log('📡 Response status:', response.status);
    console.log('📡 Response headers:', response.headers);

    const data = await handleResponse(response);
    console.log('✅ Driver vehicles fetched successfully:', data);
    console.log('📊 Data type:', typeof data);
    console.log('📊 Data length:', Array.isArray(data) ? data.length : 'Not an array');

    return data;
  } catch (error) {
    console.error('❌ Error fetching driver vehicles:', error);
    throw error;
  }
};

/**
 * Get vehicle details by registration
 */
export const getVehicleByRegistration = async (registration) => {
  try {
    console.log('🚗 Fetching vehicle details for:', registration);

    const response = await fetch(`${API_URL}/api/v1/vt/vehicles/${encodeURIComponent(registration)}`, {
      method: 'GET',
      headers: getAuthHeaders(),
      credentials: 'include',
    });

    const data = await handleResponse(response);
    console.log('✅ Vehicle details fetched successfully:', data);
    return data;
  } catch (error) {
    console.error('❌ Error fetching vehicle details:', error);
    throw error;
  }
};

// =============================================================================
// TECHNICAL CHECKUP MANAGEMENT
// =============================================================================

/**
 * Create a new technical checkup request
 */
export const createTechnicalCheckup = async (checkupData) => {
  try {
    console.log('📋 Creating technical checkup request...', checkupData);

    const response = await fetch(`${API_URL}/api/v1/vt/checkups`, {
      method: 'POST',
      headers: getAuthHeaders(),
      credentials: 'include',
      body: JSON.stringify(checkupData),
    });

    const data = await handleResponse(response);
    console.log('✅ Technical checkup created successfully:', data);
    return data;
  } catch (error) {
    console.error('❌ Error creating technical checkup:', error);
    throw error;
  }
};

/**
 * Get driver's technical checkup history
 */
export const getDriverCheckupHistory = async () => {
  try {
    console.log('📋 Fetching driver checkup history...');

    const response = await fetch(`${API_URL}/api/v1/vt/checkups/history`, {
      method: 'GET',
      headers: getAuthHeaders(),
      credentials: 'include',
    });

    const data = await handleResponse(response);
    console.log('✅ Checkup history fetched successfully:', data);
    return data;
  } catch (error) {
    console.error('❌ Error fetching checkup history:', error);
    throw error;
  }
};

/**
 * Get available technical centers
 */
export const getTechnicalCenters = async () => {
  try {
    console.log('🏢 Fetching technical centers...');

    const response = await fetch(`${API_URL}/api/v1/vt/centers`, {
      method: 'GET',
      headers: getAuthHeaders(),
      credentials: 'include',
    });

    const data = await handleResponse(response);
    console.log('✅ Technical centers fetched successfully:', data);
    return data;
  } catch (error) {
    console.error('❌ Error fetching technical centers:', error);
    throw error;
  }
};

/**
 * Get available dates for a technical center
 */
export const getAvailableDates = async (centerId) => {
  try {
    console.log('📅 Fetching available dates for center:', centerId);

    const response = await fetch(`${API_URL}/api/v1/vt/centers/${centerId}/available-dates`, {
      method: 'GET',
      headers: getAuthHeaders(),
      credentials: 'include',
    });

    const data = await handleResponse(response);
    console.log('✅ Available dates fetched successfully:', data);
    return data;
  } catch (error) {
    console.error('❌ Error fetching available dates:', error);
    throw error;
  }
};
