// Simple driver service for testing without authentication
// API URL - Now using API Gateway
const API_URL = 'http://localhost:8080';

// Get all drivers (no authentication required for testing)
export const getDriversSimple = async () => {
  try {
    console.log('🚗 Fetching drivers (simple)...');

    const response = await fetch(`${API_URL}/api/v1/driverprofile/getDrivers`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
    });

    console.log('📡 Response status:', response.status);
    console.log('📡 Response headers:', Object.fromEntries(response.headers.entries()));

    if (!response.ok) {
      const errorText = await response.text();
      console.log('❌ Error response:', errorText);
      throw new Error(`HTTP ${response.status}: ${errorText}`);
    }

    const data = await response.json();
    console.log('✅ Drivers fetched successfully:', data);
    return data;
  } catch (error) {
    console.error('❌ Error fetching drivers:', error);
    throw error;
  }
};
