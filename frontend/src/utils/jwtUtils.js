// You'll need to install jwt-decode: npm install jwt-decode
import jwt_decode from 'jwt-decode';

/**
 * Decodes a JWT token
 * @param {string} token - The JWT token to decode
 * @returns {object|null} The decoded token or null if invalid
 */
export const decodeToken = (token) => {
  try {
    return jwt_decode(token);
  } catch (error) {
    console.error('Invalid token:', error);
    return null;
  }
};

/**
 * Extracts the user role from the JWT token
 * @returns {string|null} The user role or null if not found
 */
export const getUserRole = () => {
  const token = localStorage.getItem('token');
  if (!token) return null;

  const decodedToken = decodeToken(token);
  if (!decodedToken) return null;

  // Check for authorities array in the token
  if (decodedToken.authorities && Array.isArray(decodedToken.authorities)) {
    // In your case, the authorities array directly contains the role names without ROLE_ prefix
    // Just return the first authority in the array
    if (decodedToken.authorities.length > 0) {
      return decodedToken.authorities[0];
    }
  }

  return null;
};

/**
 * Checks if the user has a specific role
 * @param {string} requiredRole - The role to check for
 * @returns {boolean} True if the user has the role, false otherwise
 */
export const hasRole = (requiredRole) => {
  const userRole = getUserRole();
  return userRole === requiredRole;
};

/**
 * Gets all user information from the token
 * @returns {object|null} User information or null if not found
 */
export const getUserInfo = () => {
  const token = localStorage.getItem('token');
  if (!token) return null;

  const decodedToken = decodeToken(token);
  if (!decodedToken) return null;

  const role = getUserRole();

  return {
    username: decodedToken.sub,
    authorities: decodedToken.authorities || [],
    role: role,
    // Add any other fields you need from the token
  };
};

/**
 * Checks if the token is expired
 * @returns {boolean} True if the token is expired, false otherwise
 */
export const isTokenExpired = () => {
  const token = localStorage.getItem('token');
  if (!token) return true;

  const decodedToken = decodeToken(token);
  if (!decodedToken || !decodedToken.exp) return true;

  // exp is in seconds, Date.now() is in milliseconds
  return decodedToken.exp * 1000 < Date.now();
};
