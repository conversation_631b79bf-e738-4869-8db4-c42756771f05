FROM eclipse-temurin:17-jdk-alpine as build
WORKDIR /workspace/app

# Copy maven executable to the image
COPY mvnw .
COPY .mvn .mvn

# Copy the pom.xml file
COPY pom.xml .

# Copy the project source
COPY src src

# Make the mvnw script executable
RUN chmod +x ./mvnw

# Package the application
RUN ./mvnw package -DskipTests

# Extract the JAR layers for better caching
RUN mkdir -p target/dependency && (cd target/dependency; jar -xf ../*.jar)

# Create a smaller runtime image
FROM eclipse-temurin:17-jre-alpine
VOLUME /tmp
ARG DEPENDENCY=/workspace/app/target/dependency

# Copy the dependency application layer by layer
COPY --from=build ${DEPENDENCY}/BOOT-INF/lib /app/lib
COPY --from=build ${DEPENDENCY}/META-INF /app/META-INF
COPY --from=build ${DEPENDENCY}/BOOT-INF/classes /app

# Set the entrypoint to run the application
ENTRYPOINT ["java","-cp","app:app/lib/*","whitecape.tech.clientauth.ClientAuthApplication"]

# Expose the port the app runs on
EXPOSE 8082

# Add health check
HEALTHCHECK --interval=30s --timeout=3s --retries=3 CMD wget -q -T 3 -O /dev/null http://localhost:8082/actuator/health || exit 1
