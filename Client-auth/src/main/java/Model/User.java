package Model;

import Model.Role;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import lombok.*;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import java.security.Principal;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;

@Data
@MappedSuperclass
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class User implements UserDetails, Principal {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    private String firstname;
    private String lastname;
    private Date dateOfBirth;  // Changed to camelCase

    @Column(unique = true)
    private String email;

    @Column(unique = true)
    private String username;

    private String password;
    private boolean accountLocked;
    private boolean enabled;
    private boolean emailVerified = false;
    private String emailVerificationToken;

    @Enumerated(EnumType.STRING)
    private Role role;  // Assuming Role is an enum you've defined

    private LocalDateTime createdDate;
    private LocalDateTime lastModifiedDate;  // Changed to camelCase

    // Principal implementation
    @Override
    public String getName() {
        return email;
    }

    // UserDetails implementation
    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return Collections.singletonList(new SimpleGrantedAuthority(role.name()));
    }

    @Override
    public boolean isAccountNonExpired() {
        return true;
    }

    @Override
    public boolean isAccountNonLocked() {
        return !accountLocked;
    }

    @Override
    public boolean isCredentialsNonExpired() {
        return true;  // Changed from false to true (typically credentials don't expire)
    }

    @Override
    public boolean isEnabled() {
        return enabled;
    }

    // Override getUsername to return email for UserDetails compatibility
    @Override
    public String getUsername() {
        return email;
    }

    // Helper method
    public String getFullName() {
        return firstname + " " + lastname;
    }


}