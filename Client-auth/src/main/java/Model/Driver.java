package Model;

// Removed DiscriminatorValue import as we're using MappedSuperclass approach
import jakarta.persistence.*;
import lombok.*;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonManagedReference;

import java.util.Date;
import java.util.List;

@Entity
@Getter
@Setter
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
@Table(name = "drivers")
@AllArgsConstructor
public class Driver extends User {

    @OneToMany(mappedBy = "driver", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonManagedReference
    private List<Vehicule> vehicleRegistration;
    // Constructor that initializes vehicleRegistration to null
    public Driver(Integer id, String firstname, String lastname, Date dateOfBirth,
                 String email, String username, String password, boolean accountLocked,
                 boolean enabled, boolean emailVerified, String emailVerificationToken,
                 Role role, java.time.LocalDateTime createdDate,
                 java.time.LocalDateTime lastModifiedDate) {
        super(id, firstname, lastname, dateOfBirth, email, username, password,
              accountLocked, enabled, emailVerified, emailVerificationToken, role, createdDate, lastModifiedDate);
        this.vehicleRegistration = null;
    }
    public Driver (){

            setRole(Role.DRIVER); // Set role for Admin
        }

    }

