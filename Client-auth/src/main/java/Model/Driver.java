package Model;

// Removed DiscriminatorValue import as we're using MappedSuperclass approach
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.ElementCollection;
import jakarta.persistence.CollectionTable;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.Column;
import lombok.*;

import java.util.Date;
import java.util.List;

@Entity
@Getter
@Setter

@Table(name = "drivers")
@AllArgsConstructor
public class Driver extends User {
    @ElementCollection
    @CollectionTable(name = "driver_vehicle_registrations", joinColumns = @JoinColumn(name = "driver_id"))
    @Column(name = "vehicle_registration")
    private List<String> vehicleRegistration;

    // Constructor that initializes vehicleRegistration to null
    public Driver(Integer id, String firstname, String lastname, Date dateOfBirth,
                 String email, String username, String password, boolean accountLocked,
                 boolean enabled, boolean emailVerified, String emailVerificationToken,
                 Role role, java.time.LocalDateTime createdDate,
                 java.time.LocalDateTime lastModifiedDate) {
        super(id, firstname, lastname, dateOfBirth, email, username, password,
              accountLocked, enabled, emailVerified, emailVerificationToken, role, createdDate, lastModifiedDate);
        this.vehicleRegistration = null;
    }
    public Driver (){

            setRole(Role.DRIVER); // Set role for Admin
        }

    }

