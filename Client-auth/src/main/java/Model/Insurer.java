package Model;

import jakarta.persistence.Entity;
@Entity

public class Insurer extends User{

 private String insuranceCompanyName;
 private String CIN;

 public Insurer() {
    }

    public Insurer(String insuranceCompanyName, String CIN) {
        this.insuranceCompanyName = insuranceCompanyName;
        this.CIN = CIN;
        setRole(Role.INSURER);
    }

    public String getInsuranceCompanyName() {
        return insuranceCompanyName;
    }

    public void setInsuranceCompanyName(String insuranceCompanyName) {
        this.insuranceCompanyName = insuranceCompanyName;
    }

    public String getCIN() {
        return CIN;
    }

    public void setCIN(String CIN) {
        this.CIN = CIN;
    }
}
