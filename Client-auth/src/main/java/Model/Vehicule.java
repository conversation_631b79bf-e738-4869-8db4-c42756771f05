package Model;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Entity
@Table(name = "vehicules")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor

public class Vehicule {


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    @Column(name = "immatriculation")
    private String immat;
    @Column(name = "vehicle_type")
    private String vehicleType;

    @Column(name = "chassis_number")
    private String chassisNumber;

    private String vehicleModel;

    private String vehicleYear;

    private String vehicleColor;
    private String vehicleFuelType;
    // If you want to link it to a driver
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "driver_id")
    private Driver driver;

}
