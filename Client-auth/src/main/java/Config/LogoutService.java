package Config;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.logout.LogoutHandler;
import org.springframework.stereotype.Service;
import token.Token;
import token.TokenRepository;

@Service
@RequiredArgsConstructor
public class LogoutService implements LogoutHandler {
    private final TokenRepository tokenRepository;

    @Override
    public void logout(
            HttpServletRequest request,
            HttpServletResponse response,
            Authentication authentication
    ) {
        final String authHeader = request.getHeader("Authorization");
        final String jwt;

        // Check if the Authorization header is present and has the correct format
        if (authHeader == null || !authHeader.startsWith("Bearer ")) {
            return;
        }

        // Extract the JWT token
        jwt = authHeader.substring(7);

        // Find and revoke the token
        var storedToken = tokenRepository.findByToken(jwt).orElse(null);
        if (storedToken != null) {
            storedToken.setExpired(true);
            storedToken.setRevoked(true);
            tokenRepository.save(storedToken);
        }

        // Clear the security context regardless of whether the token was found
        SecurityContextHolder.clearContext();

        // You could add additional logout logic here if needed
        // For example, invalidating the session
        if (request.getSession(false) != null) {
            request.getSession().invalidate();
        }

        // You could also add a cookie to clear any client-side tokens
        // For example:
        // Cookie cookie = new Cookie("jwt", null);
        // cookie.setMaxAge(0);
        // cookie.setPath("/");
        // response.addCookie(cookie);
    }

}
