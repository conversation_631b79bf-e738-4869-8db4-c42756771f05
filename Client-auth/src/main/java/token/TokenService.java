package token;

import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.Optional;

@Service
public class TokenService {
    
    private final TokenRepository tokenRepository;
    
    @Autowired
    public TokenService(TokenRepository tokenRepository) {
        this.tokenRepository = tokenRepository;
    }
    
    public boolean isTokenValid(String token) {
        return tokenRepository.findByToken(token)
                .map(t -> !t.isExpired() && !t.isRevoked())
                .orElse(false);
    }
    
    public void saveToken(Token token) {
        tokenRepository.save(token);
    }
    
    public Optional<Token> findByToken(String token) {
        return tokenRepository.findByToken(token);
    }
}
