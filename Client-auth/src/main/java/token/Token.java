package token;

import Model.*;
// Removed User import as we're only using Driver
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "tokens")
public class Token {


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(unique = true)
    private String token;

    @Enumerated(EnumType.STRING)
    private TokenType tokenType = TokenType.BEARER;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public TokenType getTokenType() {
        return tokenType;
    }

    public void setTokenType(TokenType tokenType) {
        this.tokenType = tokenType;
    }

    private boolean revoked;

    private boolean expired;
//driver role
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "driver_id")
    private Driver user;


   //Insurance
 //Insurer
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "Insurer_id")
    private Insurer insurer;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "insurance_admin_id")
    private InsuranceAdmin insuranceAdmin;

 //financialAGENT (Recette de finances)
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "financial_agent_id")
    private FinancialAGENT financialAGENT;

//TaxStickerADMIN (Administration des vignettes)
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "tax_sticker_admin_id")
    private TaxStickerADMIN taxStickerADMIN;


    //Visite technique
    //Agent
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "technical_checkup_admin_id")
    private TechnicalCheckupAdmin technicalCheckupAdmin;
    //Admin
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "technical_checkup_agent_id")
    private TechnicalCheckupAgent technicalCheckupAgent;







    public void setUser(User user) {
        if (user instanceof Driver) {
            this.user = (Driver) user;
        } else if (user instanceof Insurer) {
            this.insurer = (Insurer) user;
        } else if (user instanceof InsuranceAdmin) {
            this.insuranceAdmin = (InsuranceAdmin) user;
        } else if (user instanceof FinancialAGENT) {
            this.financialAGENT = (FinancialAGENT) user;
        } else if (user instanceof TaxStickerADMIN) {
            this.taxStickerADMIN = (TaxStickerADMIN) user;
        } else if (user instanceof TechnicalCheckupAdmin) {
            this.technicalCheckupAdmin = (TechnicalCheckupAdmin) user;
        } else if (user instanceof TechnicalCheckupAgent) {
            this.technicalCheckupAgent = (TechnicalCheckupAgent) user;
        } else {
            throw new IllegalArgumentException("Unsupported user type: " + user.getClass().getName());
        }
    }

    public boolean isRevoked() {
        return revoked;
    }

    public void setRevoked(boolean revoked) {
        this.revoked = revoked;
    }

    public boolean isExpired() {
        return expired;
    }

    public void setExpired(boolean expired) {
        this.expired = expired;
    }

    public Driver getUser() {
        return user;
    }

    public void setUser(Driver user) {
        this.user = user;
    }
    public Insurer getInsurer() {
        return insurer;
    }

    public void setInsurer(Insurer insurer) {
        this.insurer = insurer;
    }

    public InsuranceAdmin getInsuranceAdmin() {
        return insuranceAdmin;
    }

    public void setInsuranceAdmin(InsuranceAdmin insuranceAdmin) {
        this.insuranceAdmin = insuranceAdmin;
    }

    public FinancialAGENT getFinancialAGENT() {
        return financialAGENT;
    }

    public void setFinancialAGENT(FinancialAGENT financialAGENT) {
        this.financialAGENT = financialAGENT;
    }

    public TaxStickerADMIN getTaxStickerADMIN() {
        return taxStickerADMIN;
    }

    public void setTaxStickerADMIN(TaxStickerADMIN taxStickerADMIN) {
        this.taxStickerADMIN = taxStickerADMIN;
    }

    public TechnicalCheckupAdmin getTechnicalCheckupAdmin() {
        return technicalCheckupAdmin;
    }

    public void setTechnicalCheckupAdmin(TechnicalCheckupAdmin technicalCheckupAdmin) {
        this.technicalCheckupAdmin = technicalCheckupAdmin;
    }

    public TechnicalCheckupAgent getTechnicalCheckupAgent() {
        return technicalCheckupAgent;
    }

    public void setTechnicalCheckupAgent(TechnicalCheckupAgent technicalCheckupAgent) {
        this.technicalCheckupAgent = technicalCheckupAgent;
    }



}
