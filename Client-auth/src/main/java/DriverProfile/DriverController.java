package DriverProfile;

import Model.Driver;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import sharedClasses.ChangePasswordRequest;

import java.security.Principal;
import java.util.List;

@RestController
@RequestMapping("/api/v1/driverprofile")
public class DriverController {


    private final DriverService driverService;
@Autowired
    public DriverController(DriverService driverService) {
        this.driverService = driverService;
    }

    @GetMapping("/getDrivers")
    public List<Driver> getAllDrivers() {
        return driverService.getAllDrivers();
    }



    @PatchMapping("/change-password")
    public ResponseEntity<?> changePassword(
            @RequestBody ChangePasswordRequest request,
            Principal connectedUser
    ) {
        driverService.changePassword(request, connectedUser);
        return ResponseEntity.ok().build();
    }


}
