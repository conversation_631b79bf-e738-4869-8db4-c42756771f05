package DriverProfile;

import Model.Driver;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import repositories.DriverRepository;
import sharedClasses.ChangePasswordRequest;

import java.security.Principal;
import java.util.List;

@Service
public class DriverServiceImpl implements DriverService {

    private final DriverRepository driverRepository;
    private final PasswordEncoder passwordEncoder;

    public DriverServiceImpl(DriverRepository driverRepository, PasswordEncoder passwordEncoder) {
        this.driverRepository = driverRepository;
        this.passwordEncoder = passwordEncoder;
    }



    @Override
    public List<Driver> getAllDrivers() {
        return driverRepository.findAll();
    }

    @Override
    public Driver getDriverById(Integer id) {
        return driverRepository.findById(id).orElse(null);
    }

    public void changePassword(ChangePasswordRequest request, Principal connectedUser) {

        System.out.println("Connected user: " + connectedUser);

        if (connectedUser == null) {
            throw new IllegalStateException("User not authenticated");
        }

        if (!(connectedUser instanceof UsernamePasswordAuthenticationToken)) {
            throw new IllegalStateException("Invalid authentication type: " + connectedUser.getClass());
        }

        var user = (Driver) ((UsernamePasswordAuthenticationToken) connectedUser).getPrincipal();

        // check if the current password is correct
        if (!passwordEncoder.matches(request.getCurrentPassword(), user.getPassword())) {
            throw new IllegalStateException("Wrong password");
        }
        // check if the two new passwords are the same
        if (!request.getNewPassword().equals(request.getConfirmationPassword())) {
            throw new IllegalStateException("Password are not the same");
        }

        // update the password
        user.setPassword(passwordEncoder.encode(request.getNewPassword()));

        // save the new password
        driverRepository.save(user);
    }
}











