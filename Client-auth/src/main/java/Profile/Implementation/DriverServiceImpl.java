package Profile.Implementation;

import Model.Driver;
import Profile.DriverService;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import repositories.DriverRepository;
import Profile.ProfileRequests.ChangePasswordRequest;
import Email.EmailService;
import utils.TokenGenerator;

import java.security.Principal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
@Transactional
public class DriverServiceImpl implements DriverService {

    private final DriverRepository driverRepository;
    private final PasswordEncoder passwordEncoder;
    private final EmailService emailService;

    public DriverServiceImpl(DriverRepository driverRepository, PasswordEncoder passwordEncoder, EmailService emailService) {
        this.driverRepository = driverRepository;
        this.passwordEncoder = passwordEncoder;
        this.emailService = emailService;
    }



    @Override
    public List<Driver> getAllDrivers() {
        return driverRepository.findAll();
    }

    @Override
    public Driver getDriverById(Integer id) {
        return driverRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Driver not found with id: " + id));
    }

    @Override
    public Driver createDriver(Driver driver) {
        driver.setCreatedDate(LocalDateTime.now());
        driver.setLastModifiedDate(LocalDateTime.now());
        return driverRepository.save(driver);
    }

    @Override
    public Driver updateDriver(Integer id, Driver driver) {
        Driver existingDriver = getDriverById(id);

        // Update fields
        existingDriver.setFirstname(driver.getFirstname());
        existingDriver.setLastname(driver.getLastname());
        existingDriver.setDateOfBirth(driver.getDateOfBirth());
        existingDriver.setVehicleRegistration(driver.getVehicleRegistration());
        existingDriver.setLastModifiedDate(LocalDateTime.now());

        return driverRepository.save(existingDriver);
    }

    @Override
    public void deleteDriver(Integer id) {
        Driver driver = getDriverById(id);
        driverRepository.delete(driver);
    }

    // Profile management methods
    @Override
    @Transactional(readOnly = true)
    public Driver getCurrentDriverProfile(Principal connectedUser) {
        return getAuthenticatedDriver(connectedUser);
    }

    @Override
    public Driver updateDriverProfile(Driver updatedProfile, Principal connectedUser) {
        Driver currentDriver = getAuthenticatedDriver(connectedUser);

        // Update allowed fields
        currentDriver.setFirstname(updatedProfile.getFirstname());
        currentDriver.setLastname(updatedProfile.getLastname());
        currentDriver.setDateOfBirth(updatedProfile.getDateOfBirth());
        currentDriver.setVehicleRegistration(updatedProfile.getVehicleRegistration());
        currentDriver.setLastModifiedDate(LocalDateTime.now());

        return driverRepository.save(currentDriver);
    }

    @Override
    public Driver updateUsername(String newUsername, Principal connectedUser) {
        Driver driver = getAuthenticatedDriver(connectedUser);

        // Check if username is available
        if (!isUsernameAvailable(newUsername)) {
            throw new IllegalStateException("Username is already taken");
        }

        driver.setUsername(newUsername);
        driver.setLastModifiedDate(LocalDateTime.now());

        return driverRepository.save(driver);
    }

    @Override
    public Driver updatePersonalInfo(String firstname, String lastname, Date dateOfBirth, Principal connectedUser) {
        Driver driver = getAuthenticatedDriver(connectedUser);

        driver.setFirstname(firstname);
        driver.setLastname(lastname);
        driver.setDateOfBirth(dateOfBirth);
        driver.setLastModifiedDate(LocalDateTime.now());

        return driverRepository.save(driver);
    }

    @Override
    public Driver updateEmail(String newEmail, Principal connectedUser) {
        Driver driver = getAuthenticatedDriver(connectedUser);

        // Check if email is available
        if (!isEmailAvailable(newEmail)) {
            throw new IllegalStateException("Email is already in use");
        }

        String oldEmail = driver.getEmail();
        driver.setEmail(newEmail);
        driver.setEmailVerified(false); // Reset email verification
        driver.setEmailVerificationToken(TokenGenerator.generateVerificationToken());
        driver.setLastModifiedDate(LocalDateTime.now());

        Driver savedDriver = driverRepository.save(driver);

        // Send verification email for new email
        try {
            emailService.sendVerificationEmail(newEmail, savedDriver.getEmailVerificationToken());
            System.out.println("✅ Verification email sent to new email: " + newEmail);
        } catch (Exception e) {
            System.err.println("❌ Failed to send verification email: " + e.getMessage());
        }

        return savedDriver;
    }

    // Vehicle management
    @Override
    public Driver addVehicleRegistration(String vehicleRegistration, Principal connectedUser) {
        Driver driver = getAuthenticatedDriver(connectedUser);

        if (driver.getVehicleRegistration() == null) {
            driver.setVehicleRegistration(new ArrayList<>());
        }

        if (!driver.getVehicleRegistration().contains(vehicleRegistration)) {
            driver.getVehicleRegistration().add(vehicleRegistration);
            driver.setLastModifiedDate(LocalDateTime.now());
            return driverRepository.save(driver);
        }

        throw new IllegalStateException("Vehicle registration already exists");
    }

    @Override
    public Driver removeVehicleRegistration(String vehicleRegistration, Principal connectedUser) {
        Driver driver = getAuthenticatedDriver(connectedUser);

        if (driver.getVehicleRegistration() != null &&
            driver.getVehicleRegistration().remove(vehicleRegistration)) {
            driver.setLastModifiedDate(LocalDateTime.now());
            return driverRepository.save(driver);
        }

        throw new IllegalStateException("Vehicle registration not found");
    }

    @Override
    public List<String> getVehicleRegistrations(Principal connectedUser) {
        Driver driver = getAuthenticatedDriver(connectedUser);
        return driver.getVehicleRegistration() != null ?
               driver.getVehicleRegistration() : new ArrayList<>();
    }

    // Security methods
    @Override
    public void changePassword(ChangePasswordRequest request, Principal connectedUser) {
        Driver driver = getAuthenticatedDriver(connectedUser);

        // check if the current password is correct
        if (!passwordEncoder.matches(request.getCurrentPassword(), driver.getPassword())) {
            throw new IllegalStateException("Current password is incorrect");
        }

        // check if the two new passwords are the same
        if (!request.getNewPassword().equals(request.getConfirmationPassword())) {
            throw new IllegalStateException("New passwords do not match");
        }

        // update the password
        driver.setPassword(passwordEncoder.encode(request.getNewPassword()));
        driver.setLastModifiedDate(LocalDateTime.now());

        // save the new password
        driverRepository.save(driver);
    }

    @Override
    public boolean verifyCurrentPassword(String currentPassword, Principal connectedUser) {
        Driver driver = getAuthenticatedDriver(connectedUser);
        return passwordEncoder.matches(currentPassword, driver.getPassword());
    }

    // Account management
    @Override
    public void deactivateAccount(Principal connectedUser) {
        Driver driver = getAuthenticatedDriver(connectedUser);
        driver.setEnabled(false);
        driver.setLastModifiedDate(LocalDateTime.now());
        driverRepository.save(driver);
    }

    @Override
    public void reactivateAccount(Integer driverId) {
        Driver driver = getDriverById(driverId);
        driver.setEnabled(true);
        driver.setLastModifiedDate(LocalDateTime.now());
        driverRepository.save(driver);
    }

    // Profile validation
    @Override
    public boolean isUsernameAvailable(String username) {
        return driverRepository.findByUsername(username).isEmpty();
    }

    @Override
    public boolean isEmailAvailable(String email) {
        return driverRepository.findByEmail(email).isEmpty();
    }

    // Helper method to get authenticated driver
    private Driver getAuthenticatedDriver(Principal connectedUser) {
        if (connectedUser == null) {
            throw new IllegalStateException("User not authenticated");
        }

        if (!(connectedUser instanceof UsernamePasswordAuthenticationToken)) {
            throw new IllegalStateException("Invalid authentication type: " + connectedUser.getClass());
        }

        // Get the driver from the authentication token
        Driver authDriver = (Driver) ((UsernamePasswordAuthenticationToken) connectedUser).getPrincipal();

        // Fetch the driver from database to ensure lazy collections are loaded
        return driverRepository.findById(authDriver.getId())
                .orElseThrow(() -> new IllegalStateException("Driver not found in database"));
    }
}











