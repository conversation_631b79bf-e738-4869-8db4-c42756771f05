package Profile.Controllers;

import Model.Driver;
import Model.Vehicule;
import Profile.DriverService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import Profile.ProfileRequests.*;

import java.security.Principal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/v1/driverprofile")
public class DriverController {

    private final DriverService driverService;

    @Autowired
    public DriverController(DriverService driverService) {
        this.driverService = driverService;
    }

    // Basic CRUD operations
    // get all drivers
    @GetMapping("/getDrivers")
    public List<Driver> getAllDrivers() {
        return driverService.getAllDrivers();
    }
//get driver by ID
    @GetMapping("/{id}")
    public ResponseEntity<Driver> getDriverById(@PathVariable Integer id) {
        try {
            Driver driver = driverService.getDriverById(id);
            return ResponseEntity.ok(driver);
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build();
        }
    }

    @PostMapping
    public ResponseEntity<Driver> createDriver(@RequestBody Driver driver) {
        Driver createdDriver = driverService.createDriver(driver);
        return ResponseEntity.ok(createdDriver);
    }
// update driver
    @PutMapping("/{id}")
    public ResponseEntity<Driver> updateDriver(@PathVariable Integer id, @RequestBody Driver driver) {
        try {
            Driver updatedDriver = driverService.updateDriver(id, driver);
            return ResponseEntity.ok(updatedDriver);
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build();
        }
    }
// delete driver ( for testing purposes)
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteDriver(@PathVariable Integer id) {
        try {
            driverService.deleteDriver(id);
            return ResponseEntity.noContent().build();
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build();
        }
    }

    // Profile management endpoints
    @GetMapping("/profile")
    public ResponseEntity<Driver> getCurrentProfile(Principal connectedUser) {
        try {
            Driver driver = driverService.getCurrentDriverProfile(connectedUser);
            return ResponseEntity.ok(driver);
        } catch (IllegalStateException e) {
            return ResponseEntity.badRequest().build();
        }
    }

    @PutMapping("/profile")
    public ResponseEntity<Driver> updateProfile(
            @RequestBody Driver updatedProfile,
            Principal connectedUser
    ) {
        try {
            Driver driver = driverService.updateDriverProfile(updatedProfile, connectedUser);
            return ResponseEntity.ok(driver);
        } catch (IllegalStateException e) {
            return ResponseEntity.badRequest().build();
        }
    }

    @PatchMapping("/username")
    public ResponseEntity<Map<String, Object>> updateUsername(
            @RequestBody UpdateUsernameRequest request,
            Principal connectedUser
    ) {
        try {
            // Verify current password for security
            if (!driverService.verifyCurrentPassword(request.getCurrentPassword(), connectedUser)) {
                return ResponseEntity.badRequest().body(Map.of("error", "Current password is incorrect"));
            }

            Driver driver = driverService.updateUsername(request.getNewUsername(), connectedUser);

            Map<String, Object> response = new HashMap<>();
            response.put("message", "Username updated successfully");
            response.put("newUsername", driver.getUsername());

            return ResponseEntity.ok(response);
        } catch (IllegalStateException e) {
            return ResponseEntity.badRequest().body(Map.of("error", e.getMessage()));
        }
    }

    @PatchMapping("/personal-info")
    public ResponseEntity<Map<String, Object>> updatePersonalInfo(
            @RequestBody UpdatePersonalInfoRequest request,
            Principal connectedUser
    ) {
        try {
            Driver driver = driverService.updatePersonalInfo(
                request.getFirstname(),
                request.getLastname(),
                request.getDateOfBirth(),
                connectedUser
            );

            Map<String, Object> response = new HashMap<>();
            response.put("message", "Personal information updated successfully");
            response.put("firstname", driver.getFirstname());
            response.put("lastname", driver.getLastname());
            response.put("dateOfBirth", driver.getDateOfBirth());

            return ResponseEntity.ok(response);
        } catch (IllegalStateException e) {
            return ResponseEntity.badRequest().body(Map.of("error", e.getMessage()));
        }
    }

    @PatchMapping("/email")
    public ResponseEntity<Map<String, Object>> updateEmail(
            @RequestBody UpdateEmailRequest request,
            Principal connectedUser
    ) {
        try {
            // Verify current password for security
            if (!driverService.verifyCurrentPassword(request.getCurrentPassword(), connectedUser)) {
                return ResponseEntity.badRequest().body(Map.of("error", "Current password is incorrect"));
            }

            Driver driver = driverService.updateEmail(request.getNewEmail(), connectedUser);

            Map<String, Object> response = new HashMap<>();
            response.put("message", "Email updated successfully. Please verify your new email address.");
            response.put("newEmail", driver.getEmail());
            response.put("emailVerified", driver.isEmailVerified());

            return ResponseEntity.ok(response);
        } catch (IllegalStateException e) {
            return ResponseEntity.badRequest().body(Map.of("error", e.getMessage()));
        }
    }

    // Vehicle management endpoints
    @PostMapping("/vehicles")
    public ResponseEntity<Map<String, Object>> addVehicleRegistration(
            @RequestBody VehicleRegistrationRequest request,
            Principal connectedUser
    ) {
        try {
            Driver driver = driverService.addVehicleRegistration(request.getVehicleRegistration(), connectedUser);

            Map<String, Object> response = new HashMap<>();
            response.put("message", "Vehicle registration added successfully");
            response.put("vehicleRegistrations", driver.getVehicleRegistration());

            return ResponseEntity.ok(response);
        } catch (IllegalStateException e) {
            return ResponseEntity.badRequest().body(Map.of("error", e.getMessage()));
        }
    }

    @DeleteMapping("/vehicles/{vehicleRegistration}")
    public ResponseEntity<Map<String, Object>> removeVehicleRegistration(
            @PathVariable String vehicleRegistration,
            Principal connectedUser
    ) {
        try {
            Driver driver = driverService.removeVehicleRegistration(vehicleRegistration, connectedUser);

            Map<String, Object> response = new HashMap<>();
            response.put("message", "Vehicle registration removed successfully");
            response.put("vehicleRegistrations", driver.getVehicleRegistration());

            return ResponseEntity.ok(response);
        } catch (IllegalStateException e) {
            return ResponseEntity.badRequest().body(Map.of("error", e.getMessage()));
        }
    }

    @GetMapping("/vehicles")
    public ResponseEntity<List<Vehicule>> getVehicleRegistrations(Principal connectedUser) {
        try {
            List<Vehicule> vehicles = driverService.getVehicleRegistrations(connectedUser);
            return ResponseEntity.ok(vehicles);
        } catch (IllegalStateException e) {
            return ResponseEntity.badRequest().build();
        }
    }

    // Security endpoints
    @PatchMapping("/change-password")
    public ResponseEntity<Map<String, String>> changePassword(
            @RequestBody ChangePasswordRequest request,
            Principal connectedUser
    ) {
        try {
            driverService.changePassword(request, connectedUser);
            return ResponseEntity.ok(Map.of("message", "Password changed successfully"));
        } catch (IllegalStateException e) {
            return ResponseEntity.badRequest().body(Map.of("error", e.getMessage()));
        }
    }

    @PostMapping("/verify-password")
    public ResponseEntity<Map<String, Boolean>> verifyPassword(
            @RequestBody Map<String, String> request,
            Principal connectedUser
    ) {
        try {
            boolean isValid = driverService.verifyCurrentPassword(request.get("password"), connectedUser);
            return ResponseEntity.ok(Map.of("valid", isValid));
        } catch (IllegalStateException e) {
            return ResponseEntity.badRequest().body(Map.of("valid", false));
        }
    }

    // Account management endpoints
    @PatchMapping("/deactivate")
    public ResponseEntity<Map<String, String>> deactivateAccount(Principal connectedUser) {
        try {
            driverService.deactivateAccount(connectedUser);
            return ResponseEntity.ok(Map.of("message", "Account deactivated successfully"));
        } catch (IllegalStateException e) {
            return ResponseEntity.badRequest().body(Map.of("error", e.getMessage()));
        }
    }

    @PatchMapping("/reactivate/{driverId}")
    public ResponseEntity<Map<String, String>> reactivateAccount(@PathVariable Integer driverId) {
        try {
            driverService.reactivateAccount(driverId);
            return ResponseEntity.ok(Map.of("message", "Account reactivated successfully"));
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().body(Map.of("error", e.getMessage()));
        }
    }

    // Validation endpoints
    @GetMapping("/check-username")
    public ResponseEntity<Map<String, Boolean>> checkUsernameAvailability(@RequestParam String username) {
        boolean available = driverService.isUsernameAvailable(username);
        return ResponseEntity.ok(Map.of("available", available));
    }

    @GetMapping("/check-email")
    public ResponseEntity<Map<String, Boolean>> checkEmailAvailability(@RequestParam String email) {
        boolean available = driverService.isEmailAvailable(email);
        return ResponseEntity.ok(Map.of("available", available));
    }
}
