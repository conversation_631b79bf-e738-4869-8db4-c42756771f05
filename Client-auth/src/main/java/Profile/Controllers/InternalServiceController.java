package Profile.Controllers;

import Model.Driver;
import Profile.DriverService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/v1/internal")
public class InternalServiceController {

    @Autowired
    private DriverService driverService;

    @Value("${service.internal.token:vt-service-internal-token}")
    private String expectedInternalToken;

    /**
     * Get driver vehicles for internal service calls
     * This endpoint is used by other microservices (like VT service)
     */
    @GetMapping("/driver/vehicles")
    public ResponseEntity<List<String>> getDriverVehiclesInternal(
            @RequestHeader("X-User-Email") String userEmail,
            @RequestHeader("X-Service-Token") String serviceToken,
            @RequestHeader(value = "X-Requesting-Service", required = false) String requestingService) {

        System.out.println("📞 Internal API: Received request from: " + requestingService);
        System.out.println("📝 Internal API: User email: " + userEmail);
        System.out.println("🔑 Internal API: Service token: " + serviceToken);
        System.out.println("🔍 Internal API: Expected token: " + expectedInternalToken);

        // Validate service token
        if (!expectedInternalToken.equals(serviceToken)) {
            System.err.println("❌ Internal API: Invalid service token!");
            return ResponseEntity.status(401).build();
        }

        System.out.println("✅ Internal API: Service token validated");

        try {
            // Find driver by email
            System.out.println("🔍 Internal API: Looking for driver with email: " + userEmail);
            Driver driver = driverService.findDriverByEmail(userEmail);
            if (driver == null) {
                System.err.println("❌ Internal API: Driver not found for email: " + userEmail);
                return ResponseEntity.notFound().build();
            }

            System.out.println("✅ Internal API: Found driver: " + driver.getFirstname() + " " + driver.getLastname());

            // Get vehicle registrations
            List<String> vehicles = driverService.getVehicleRegistrationsByDriver(driver);

            System.out.println("🚗 Internal API: Found " + vehicles.size() + " vehicles: " + vehicles);
            System.out.println("✅ Internal service call from: " + requestingService +
                             " for user: " + userEmail +
                             " returned " + vehicles.size() + " vehicles");

            return ResponseEntity.ok(vehicles);
        } catch (Exception e) {
            System.err.println("❌ Internal API: Error in internal service call: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(500).build();
        }
    }

    /**
     * Get driver profile for internal service calls
     */
    @GetMapping("/driver/profile")
    public ResponseEntity<Map<String, Object>> getDriverProfileInternal(
            @RequestHeader("X-User-Email") String userEmail,
            @RequestHeader("X-Service-Token") String serviceToken,
            @RequestHeader(value = "X-Requesting-Service", required = false) String requestingService) {

        // Validate service token
        if (!expectedInternalToken.equals(serviceToken)) {
            return ResponseEntity.status(401).build();
        }

        try {
            Driver driver = driverService.findDriverByEmail(userEmail);
            if (driver == null) {
                return ResponseEntity.notFound().build();
            }

            Map<String, Object> profile = new HashMap<>();
            profile.put("id", driver.getId());
            profile.put("email", driver.getEmail());
            profile.put("firstname", driver.getFirstname());
            profile.put("lastname", driver.getLastname());
            profile.put("username", driver.getUsername());

            return ResponseEntity.ok(profile);
        } catch (Exception e) {
            System.err.println("Error in internal profile service call: " + e.getMessage());
            return ResponseEntity.status(500).build();
        }
    }

    /**
     * Health check for internal services
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, String>> internalHealth() {
        Map<String, String> response = new HashMap<>();
        response.put("status", "UP");
        response.put("service", "Internal Service API");
        return ResponseEntity.ok(response);
    }
}
