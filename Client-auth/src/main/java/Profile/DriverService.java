package Profile;

import Model.Driver;
import Model.Vehicule;
import Profile.ProfileRequests.ChangePasswordRequest;
import Profile.ProfileRequests.DriverProfileResponse;

import java.security.Principal;
import java.util.Date;
import java.util.List;

public interface DriverService {

    // Basic CRUD operations
    List<Driver> getAllDrivers();
    Driver getDriverById(Integer id);
    Driver createDriver(Driver driver);
    Driver updateDriver(Integer id, Driver driver);
    void deleteDriver(Integer id);

    // Profile management methods
    DriverProfileResponse getCurrentDriverProfile(Principal connectedUser);
    Driver updateDriverProfile(Driver updatedProfile, Principal connectedUser);
    Driver updateUsername(String newUsername, Principal connectedUser);
    Driver updatePersonalInfo(String firstname, String lastname, Date dateOfBirth, Principal connectedUser);
    Driver updateEmail(String newEmail, Principal connectedUser);

    // Vehicle management
    Driver addVehicleRegistration(String vehicleRegistration, Principal connectedUser);
    Driver removeVehicleRegistration(String vehicleRegistration, Principal connectedUser);
    List<String> getVehicleRegistrations(Principal connectedUser);

    // Security methods
    void changePassword(ChangePasswordRequest request, Principal connectedUser);
    boolean verifyCurrentPassword(String currentPassword, Principal connectedUser);

    // Account management
    void deactivateAccount(Principal connectedUser);
    void reactivateAccount(Integer driverId); // Admin only

    // Profile validation
    boolean isUsernameAvailable(String username);
    boolean isEmailAvailable(String email);
}
