package Email;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.stereotype.Service;

@Service
public class EmailService {

    @Autowired
    private JavaMailSender mailSender;

    @Value("${app.base-url:http://localhost:8082}")
    private String baseUrl;

    @Value("${spring.mail.username}")
    private String fromEmail;

    public void sendVerificationEmail(String toEmail, String verificationToken) {
        try {
            SimpleMailMessage message = new SimpleMailMessage();
            message.setFrom(fromEmail);
            message.setTo(toEmail);
            message.setSubject("Email Verification - Tunichain Application");
            
            String verificationUrl = baseUrl + "/api/v1/auth/verify-email?token=" + verificationToken;
            
            String emailBody = "Dear User,\n\n" +
                    "Thank you for registering with <PERSON><PERSON>hain!\n\n" +
                    "Please click the link below to verify your email address:\n" +
                    verificationUrl + "\n\n" +
                    "This link will expire in 24 hours.\n\n" +
                    "If you didn't create an account with us, please ignore this email.\n\n" +
                    "Best regards,\n" +
                    "TaxStick Team";
            
            message.setText(emailBody);
            
            mailSender.send(message);
            System.out.println("✅ Verification email sent successfully to: " + toEmail);
            
        } catch (Exception e) {
            System.err.println("❌ Failed to send verification email to: " + toEmail);
            System.err.println("Error: " + e.getMessage());
            throw new RuntimeException("Failed to send verification email", e);
        }
    }

    public void sendWelcomeEmail(String toEmail, String firstName) {
        try {
            SimpleMailMessage message = new SimpleMailMessage();
            message.setFrom(fromEmail);
            message.setTo(toEmail);
            message.setSubject("Welcome to TaxStick!");
            
            String emailBody = "Dear " + firstName + ",\n\n" +
                    "Welcome to Tunichain! Your email has been successfully verified.\n\n" +
                    "You can now access all features of our platform.\n\n" +
                    "Best regards,\n" +
                    "TaxStick Team";
            
            message.setText(emailBody);
            
            mailSender.send(message);
            System.out.println("✅ Welcome email sent successfully to: " + toEmail);
            
        } catch (Exception e) {
            System.err.println("❌ Failed to send welcome email to: " + toEmail);
            System.err.println("Error: " + e.getMessage());
        }
    }
}
