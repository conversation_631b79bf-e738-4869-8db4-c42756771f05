package profile;

import Model.Driver;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import repositories.DriverRepository;

import java.util.List;

@Service
public class DriverServiceImpl implements DriverService {

private final DriverRepository driverRepository;

@Autowired
public DriverServiceImpl(DriverRepository driverRepository) {
    this.driverRepository = driverRepository;
}

@Override
     public List<Driver> getAllDrivers() {
        return driverRepository.findAll();
    }

    @Override
    public Driver getDriverById(Integer id) {
        return driverRepository.findById(id).orElse(null);
    }











}
