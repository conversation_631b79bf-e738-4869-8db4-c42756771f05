package profile;

import Model.Driver;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/api/v1/driverprofile")
public class DriverController {


    private final DriverService driverService;

    public DriverController(DriverService driverService) {
        this.driverService = driverService;
    }
  @GetMapping("/getDrivers")
    public List<Driver> getAllDrivers() {
        return driverService.getAllDrivers();
    }





}
