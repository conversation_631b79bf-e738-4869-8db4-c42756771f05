package auth;


import Model.Role;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RegisterRequest {

    private String firstname;
    private String lastname;
    private String email;
    private String username; // Added username field
    private String password;
    private Role role;
    private java.util.Date dateOfBirth; // Added dateOfBirth field
}