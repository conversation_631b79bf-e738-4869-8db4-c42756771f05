package auth;

import Config.JwtService;

import Model.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import repositories.*;
import services.EmailService;
import token.Token;
import utils.TokenGenerator;
import token.TokenRepository;
import token.TokenType;

import java.io.IOException;
import com.fasterxml.jackson.databind.ObjectMapper;

@Service
@RequiredArgsConstructor
public class AuthenticationService {

    private final DriverRepository driverRepository; // Repository for Driver
    private final InsurerRepository adminRepository;   // Repository for Insurer
    private final Insuranceadmin insuranceAdminRepository; // Repository for InsuranceAdmin

    private final VignetteWorkerRepository vwRepository; // financialAgent aka vignette worker
    private final VignetteAdminRepository tsRepository; // vignettes admin

    private final TCworkerRepository tcRepository; // agent visite technique
    private final TCAdmin tcAdminRepository;    // Admin visite technique

    private final TokenRepository tokenRepository;
    private final PasswordEncoder passwordEncoder;
    private final JwtService jwtService;
    private final AuthenticationManager authenticationManager;
    private final EmailService emailService;


    @Transactional
    public AuthenticationResponse register(RegisterRequest request) {
        // Determine the user type based on the role
        User user;
        switch (request.getRole()) {
            case DRIVER:
                var driver = new Driver();
                 // Set driver-specific attribute
                user = driver;
                break;

            case INSURER:
                var admin = new Insurer();
                user = admin;
                break;
            case FINANCIAL_AGENT:
                var financialAgent = new FinancialAGENT();
                user = financialAgent;
                break;
            case TC_AGENT:
                var technicalCheckupAgent = new TechnicalCheckupAgent();
                    user = technicalCheckupAgent;
                    break;
            case VIGNETTE_ADMIN:
                var taxStickerADMIN = new TaxStickerADMIN();
                user = taxStickerADMIN;
                break;
            case TC_ADMIN:
                    var technicalCheckupAdmin = new TechnicalCheckupAdmin();
                     user = technicalCheckupAdmin;
                     break;

            case INSURANCE_ADMIN:
                    var insuranceAdmin = new InsuranceAdmin();
                    user = insuranceAdmin;
                    break;

            default:
                throw new IllegalArgumentException("Invalid role: " + request.getRole());
        }

        // Set common attributes for all user types
        user.setFirstname(request.getFirstname());
        user.setLastname(request.getLastname());
        user.setEmail(request.getEmail());
        user.setUsername(request.getUsername() != null ? request.getUsername() : request.getEmail()); // Use provided username or fallback to email
        user.setPassword(passwordEncoder.encode(request.getPassword()));
        user.setRole(request.getRole());
        user.setEnabled(true); // Keep user enabled but track email verification separately
        user.setDateOfBirth(request.getDateOfBirth() != null ? request.getDateOfBirth() : new java.util.Date()); // Use provided date of birth or default
        user.setAccountLocked(false); // Ensure account is not locked
        user.setEmailVerified(false); // Email not verified initially
        user.setEmailVerificationToken(TokenGenerator.generateVerificationToken()); // Generate verification token
        user.setCreatedDate(java.time.LocalDateTime.now()); // Set creation date
        user.setLastModifiedDate(java.time.LocalDateTime.now()); // Set last modified date

        // Save the user based on their type
        User savedUser;
        if (user instanceof Driver) {
            savedUser = driverRepository.save((Driver) user);
        }
        // Insurer
        else if (user instanceof Insurer) {
            savedUser = adminRepository.save((Insurer) user);
        }
        //FinancialAGENT ( vingnettes worker)
        else if (user instanceof FinancialAGENT) {
            savedUser = vwRepository.save((FinancialAGENT) user);
        }
        //TechnicalCheckupAgent ( agent visite technique)
        else if (user instanceof TechnicalCheckupAgent) {
            savedUser = tcRepository.save((TechnicalCheckupAgent) user);
        }
        //TaxStickerADMIN ( Admin vignette)
        else if (user instanceof TaxStickerADMIN) {
            savedUser = tsRepository.save((TaxStickerADMIN) user);
        }
        //TechnicalCheckupAdmin ( Admin visite technique)
        else if (user instanceof TechnicalCheckupAdmin) {
            savedUser = tcAdminRepository.save((TechnicalCheckupAdmin) user);
        }
        //InsuranceAdmin ( Admin assurance)
        else if (user instanceof InsuranceAdmin) {
            savedUser = insuranceAdminRepository.save((InsuranceAdmin) user);
        }

        else {
            throw new IllegalStateException("Unsupported user type");
        }

        // Send verification email
        try {
            emailService.sendVerificationEmail(savedUser.getEmail(), savedUser.getEmailVerificationToken());
            System.out.println("✅ Verification email sent to: " + savedUser.getEmail());
        } catch (Exception e) {
            System.err.println("❌ Failed to send verification email: " + e.getMessage());
            // Note: We don't throw here to avoid registration failure due to email issues
        }

        // Generate JWT tokens (user can still get tokens but account is disabled until verified)
        var jwtToken = jwtService.generateToken(savedUser);
        var refreshToken = jwtService.generateRefreshToken(savedUser);

        // Save the token
        saveUserToken(savedUser, jwtToken);

        // Return the response with email verification status
        return AuthenticationResponse.builder()
                .accessToken(jwtToken)
                .refreshToken(refreshToken)
                .emailVerified(savedUser.isEmailVerified())
                .message("Registration successful! Please check your email to verify your account.")
                .build();
    }

    public AuthenticationResponse authenticate(AuthenticationRequest request) {
        System.out.println("🔐 Attempting authentication for: " + request.getEmail());

        // First check if user exists and get their status
        var user = driverRepository.findByEmail(request.getEmail())
                .orElseThrow(() -> new RuntimeException("User not found"));

        System.out.println("👤 User found: " + user.getEmail());
        System.out.println("✅ User enabled: " + user.isEnabled());
        System.out.println("✅ Email verified: " + user.isEmailVerified());
        System.out.println("🔒 Account locked: " + user.isAccountLocked());

        authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(
                        request.getEmail(),
                        request.getPassword()
                )
        );
        var jwtToken = jwtService.generateToken(user);
        var refreshToken = jwtService.generateRefreshToken(user);
        revokeAllUserTokens(user);
        saveUserToken(user, jwtToken);

        // Prepare response with email verification status
        String message = user.isEmailVerified() ?
            "Authentication successful" :
            "Authentication successful, but please verify your email to access all features";

        return AuthenticationResponse.builder()
                .accessToken(jwtToken)
                .refreshToken(refreshToken)
                .emailVerified(user.isEmailVerified())
                .message(message)
                .build();
    }

    private void saveUserToken(User user, String jwtToken) {
        var token = Token.builder()
                .token(jwtToken)
                .tokenType(TokenType.BEARER)
                .expired(false)
                .revoked(false)
                .build();

        // Set the user based on its type
        token.setUser(user);

        // Save the token
        tokenRepository.save(token);
    }

    private void revokeAllUserTokens(User user) {
        var validUserTokens = tokenRepository.findAllValidTokenByUser(user.getId());
        if (validUserTokens.isEmpty())
            return;
        validUserTokens.forEach(token -> {
            token.setExpired(true);
            token.setRevoked(true);
        });
        tokenRepository.saveAll(validUserTokens);
    }

    /**
     * Revokes a specific token
     * @param token The JWT token to revoke
     * @return true if the token was found and revoked, false otherwise
     */
    public boolean revokeToken(String token) {
        var storedToken = tokenRepository.findByToken(token).orElse(null);
        if (storedToken != null) {
            storedToken.setExpired(true);
            storedToken.setRevoked(true);
            tokenRepository.save(storedToken);
            return true;
        }
        return false;
    }

    public void refreshToken(
            HttpServletRequest request,
            HttpServletResponse response
    ) throws IOException {
        final String authHeader = request.getHeader(HttpHeaders.AUTHORIZATION);
        final String refreshToken;
        final String userEmail;
        if (authHeader == null || !authHeader.startsWith("Bearer ")) {
            return;
        }
        refreshToken = authHeader.substring(7);
        userEmail = jwtService.extractUsername(refreshToken);
        if (userEmail != null) {
            var user = driverRepository.findByEmail(userEmail)
                    .orElseThrow(() -> new RuntimeException("User not found"));
            if (jwtService.isTokenValid(refreshToken, user)) {
                var accessToken = jwtService.generateToken(user);
                revokeAllUserTokens(user);
                saveUserToken(user, accessToken);
                var authResponse = AuthenticationResponse.builder()
                        .accessToken(accessToken)
                        .refreshToken(refreshToken)
                        .build();
                new ObjectMapper().writeValue(response.getOutputStream(), authResponse);
            }
        }
    }

    /**
     * Verify email using the verification token
     */
    public boolean verifyEmail(String token) {
        try {
            // Try to find the user by verification token in Driver repository
            var driverOpt = driverRepository.findByEmailVerificationToken(token);
            if (driverOpt.isPresent()) {
                var driver = driverOpt.get();
                driver.setEmailVerified(true);
                driver.setEnabled(true);
                driver.setEmailVerificationToken(null); // Clear the token
                driverRepository.save(driver);

                // Send welcome email
                try {
                    emailService.sendWelcomeEmail(driver.getEmail(), driver.getFirstname());
                } catch (Exception e) {
                    System.err.println("❌ Failed to send welcome email: " + e.getMessage());
                }

                return true;
            }

            // Try to find the user by verification token in Insurer repository
            var insurerOpt = adminRepository.findByEmailVerificationToken(token);
            if (insurerOpt.isPresent()) {
                var insurer = insurerOpt.get();
                insurer.setEmailVerified(true);
                insurer.setEnabled(true);
                insurer.setEmailVerificationToken(null); // Clear the token
                adminRepository.save(insurer);

                // Send welcome email
                try {
                    emailService.sendWelcomeEmail(insurer.getEmail(), insurer.getFirstname());
                } catch (Exception e) {
                    System.err.println("❌ Failed to send welcome email: " + e.getMessage());
                }

                return true;
            }

            return false; // Token not found
        } catch (Exception e) {
            System.err.println("❌ Error during email verification: " + e.getMessage());
            return false;
        }
    }

    /**
     * Resend verification email
     */
    public boolean resendVerificationEmail(String email) {
        try {
            // Try to find the user in Driver repository
            var driverOpt = driverRepository.findByEmail(email);
            if (driverOpt.isPresent()) {
                var driver = driverOpt.get();
                if (driver.isEmailVerified()) {
                    return false; // Already verified
                }

                // Generate new token
                driver.setEmailVerificationToken(TokenGenerator.generateVerificationToken());
                driverRepository.save(driver);

                // Send email
                emailService.sendVerificationEmail(driver.getEmail(), driver.getEmailVerificationToken());
                return true;
            }

            // Try to find the user in Insurer repository
            var insurerOpt = adminRepository.findByEmail(email);
            if (insurerOpt.isPresent()) {
                var insurer = insurerOpt.get();
                if (insurer.isEmailVerified()) {
                    return false; // Already verified
                }

                // Generate new token
                insurer.setEmailVerificationToken(TokenGenerator.generateVerificationToken());
                adminRepository.save(insurer);

                // Send email
                emailService.sendVerificationEmail(insurer.getEmail(), insurer.getEmailVerificationToken());
                return true;
            }

            return false; // User not found
        } catch (Exception e) {
            System.err.println("❌ Error resending verification email: " + e.getMessage());
            return false;
        }
    }

    /**
     * Check if email is verified
     */
    public boolean isEmailVerified(String email) {
        try {
            // Try to find the user in Driver repository
            var driverOpt = driverRepository.findByEmail(email);
            if (driverOpt.isPresent()) {
                return driverOpt.get().isEmailVerified();
            }

            // Try to find the user in Insurer repository
            var insurerOpt = adminRepository.findByEmail(email);
            if (insurerOpt.isPresent()) {
                return insurerOpt.get().isEmailVerified();
            }

            return false; // User not found
        } catch (Exception e) {
            System.err.println("❌ Error checking email verification status: " + e.getMessage());
            return false;
        }
    }
}