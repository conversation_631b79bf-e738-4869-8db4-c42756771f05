package auth;

import Config.JwtService;

import Model.Driver;
import Model.Insurer;
import Model.User;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import repositories.DriverRepository;
import repositories.InsurerRepository;
import token.Token;
import token.TokenRepository;
import token.TokenType;

import java.io.IOException;
import com.fasterxml.jackson.databind.ObjectMapper;

@Service
@RequiredArgsConstructor
public class AuthenticationService {

    private final DriverRepository driverRepository; // Repository for Driver
    private final InsurerRepository adminRepository;   // Repository for Admin
    private final TokenRepository tokenRepository;
    private final PasswordEncoder passwordEncoder;
    private final JwtService jwtService;
    private final AuthenticationManager authenticationManager;

    @Transactional
    public AuthenticationResponse register(RegisterRequest request) {
        // Determine the user type based on the role
        User user;
        switch (request.getRole()) {
            case DRIVER:
                var driver = new Driver();
                 // Set driver-specific attribute
                user = driver;
                break;

            case INSURER:
                var admin = new Insurer();
                user = admin;
                break;

            default:
                throw new IllegalArgumentException("Invalid role: " + request.getRole());
        }

        // Set common attributes for all user types
        user.setFirstname(request.getFirstname());
        user.setLastname(request.getLastname());
        user.setEmail(request.getEmail());
        user.setUsername(request.getUsername() != null ? request.getUsername() : request.getEmail()); // Use provided username or fallback to email
        user.setPassword(passwordEncoder.encode(request.getPassword()));
        user.setRole(request.getRole());
        user.setEnabled(true); // Enable the user by default
        user.setDateOfBirth(request.getDateOfBirth() != null ? request.getDateOfBirth() : new java.util.Date()); // Use provided date of birth or default
        user.setAccountLocked(false); // Ensure account is not locked
        user.setCreatedDate(java.time.LocalDateTime.now()); // Set creation date
        user.setLastModifiedDate(java.time.LocalDateTime.now()); // Set last modified date

        // Save the user based on their type
        User savedUser;
        if (user instanceof Driver) {
            savedUser = driverRepository.save((Driver) user);
        } else if (user instanceof Insurer) {
            savedUser = adminRepository.save((Insurer) user);
        } else {
            throw new IllegalStateException("Unsupported user type");
        }

        // Generate JWT tokens
        var jwtToken = jwtService.generateToken(savedUser);
        var refreshToken = jwtService.generateRefreshToken(savedUser);

        // Save the token
        saveUserToken(savedUser, jwtToken);

        // Return the response
        return AuthenticationResponse.builder()
                .accessToken(jwtToken)
                .refreshToken(refreshToken)
                .build();
    }

    public AuthenticationResponse authenticate(AuthenticationRequest request) {
        authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(
                        request.getEmail(),
                        request.getPassword()
                )
        );
        var user = driverRepository.findByEmail(request.getEmail())
                .orElseThrow(() -> new RuntimeException("User not found"));
        var jwtToken = jwtService.generateToken(user);
        var refreshToken = jwtService.generateRefreshToken(user);
        revokeAllUserTokens(user);
        saveUserToken(user, jwtToken);
        return AuthenticationResponse.builder()
                .accessToken(jwtToken)
                .refreshToken(refreshToken)
                .build();
    }

    private void saveUserToken(User user, String jwtToken) {
        var token = Token.builder()
                .token(jwtToken)
                .tokenType(TokenType.BEARER)
                .expired(false)
                .revoked(false)
                .build();

        // Set the user based on its type
        token.setUser(user);

        // Save the token
        tokenRepository.save(token);
    }

    private void revokeAllUserTokens(User user) {
        var validUserTokens = tokenRepository.findAllValidTokenByUser(user.getId());
        if (validUserTokens.isEmpty())
            return;
        validUserTokens.forEach(token -> {
            token.setExpired(true);
            token.setRevoked(true);
        });
        tokenRepository.saveAll(validUserTokens);
    }

    /**
     * Revokes a specific token
     * @param token The JWT token to revoke
     * @return true if the token was found and revoked, false otherwise
     */
    public boolean revokeToken(String token) {
        var storedToken = tokenRepository.findByToken(token).orElse(null);
        if (storedToken != null) {
            storedToken.setExpired(true);
            storedToken.setRevoked(true);
            tokenRepository.save(storedToken);
            return true;
        }
        return false;
    }

    public void refreshToken(
            HttpServletRequest request,
            HttpServletResponse response
    ) throws IOException {
        final String authHeader = request.getHeader(HttpHeaders.AUTHORIZATION);
        final String refreshToken;
        final String userEmail;
        if (authHeader == null || !authHeader.startsWith("Bearer ")) {
            return;
        }
        refreshToken = authHeader.substring(7);
        userEmail = jwtService.extractUsername(refreshToken);
        if (userEmail != null) {
            var user = driverRepository.findByEmail(userEmail)
                    .orElseThrow(() -> new RuntimeException("User not found"));
            if (jwtService.isTokenValid(refreshToken, user)) {
                var accessToken = jwtService.generateToken(user);
                revokeAllUserTokens(user);
                saveUserToken(user, accessToken);
                var authResponse = AuthenticationResponse.builder()
                        .accessToken(accessToken)
                        .refreshToken(refreshToken)
                        .build();
                new ObjectMapper().writeValue(response.getOutputStream(), authResponse);
            }
        }
    }
}