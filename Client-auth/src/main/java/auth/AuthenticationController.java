package auth;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.security.Principal;
import java.util.HashMap;
import java.util.Map;

import java.io.IOException;

@RestController
@RequestMapping("/api/v1/auth")

@RequiredArgsConstructor
public class AuthenticationController {



    private final AuthenticationService service;

//works like a charm
    @PostMapping("/register")
    public ResponseEntity<AuthenticationResponse> register(
            @RequestBody RegisterRequest request
    ) {
        return ResponseEntity.ok(service.register(request));
    }

    //works like a charm
    @PostMapping("/authenticate")
    public ResponseEntity<AuthenticationResponse> authenticate(
            @RequestBody AuthenticationRequest request
    ) {
        return ResponseEntity.ok(service.authenticate(request));
    }

    @PostMapping("/refresh-token")
    public void refreshToken(
            HttpServletRequest request,
            HttpServletResponse response
    ) throws IOException {
        service.refreshToken(request, response);
    }

    @PostMapping("/test")
    public ResponseEntity<String> testEndpoint() {
        return ResponseEntity.ok("Auth endpoint is accessible without authentication");
    }

    @PostMapping("/logout")
    public ResponseEntity<String> logout(
            HttpServletRequest request,
            HttpServletResponse response,
            Authentication authentication
    ) {
        // The actual logout is handled by Spring Security's LogoutFilter
        // This endpoint just returns a success message
        return ResponseEntity.ok("Logged out successfully");
    }

    @GetMapping("/verify-email")
    public ResponseEntity<String> verifyEmail(@RequestParam("token") String token) {
        boolean isVerified = service.verifyEmail(token);

        if (isVerified) {
            return ResponseEntity.ok(
                "<html><body>" +
                "<h2>Email Verified Successfully!</h2>" +
                "<p>Your email has been verified. You can now access all features of TaxStick.</p>" +
                "<p><a href='http://localhost:3000/login'>Click here to login</a></p>" +
                "</body></html>"
            );
        } else {
            return ResponseEntity.badRequest().body(
                "<html><body>" +
                "<h2>Email Verification Failed</h2>" +
                "<p>The verification link is invalid or has expired.</p>" +
                "<p><a href='http://localhost:3000/resend-verification'>Click here to resend verification email</a></p>" +
                "</body></html>"
            );
        }
    }

    @PostMapping("/resend-verification")
    public ResponseEntity<String> resendVerificationEmail(@RequestBody Map<String, String> request) {
        String email = request.get("email");

        if (email == null || email.trim().isEmpty()) {
            return ResponseEntity.badRequest().body("Email is required");
        }

        boolean isSent = service.resendVerificationEmail(email);

        if (isSent) {
            return ResponseEntity.ok("Verification email sent successfully");
        } else {
            return ResponseEntity.badRequest().body("Failed to send verification email. User not found or already verified.");
        }
    }

    @GetMapping("/email-verification-status")
    public ResponseEntity<Map<String, Object>> getEmailVerificationStatus(
            @RequestParam("email") String email) {

        boolean isVerified = service.isEmailVerified(email);

        Map<String, Object> response = new HashMap<>();
        response.put("email", email);
        response.put("verified", isVerified);
        response.put("message", isVerified ? "Email is verified" : "Email is not verified");

        return ResponseEntity.ok(response);
    }

    @GetMapping("/debug-token")
    public ResponseEntity<Map<String, Object>> debugToken(Principal connectedUser) {
        Map<String, Object> response = new HashMap<>();

        if (connectedUser == null) {
            response.put("error", "No authenticated user found");
            return ResponseEntity.badRequest().body(response);
        }

        if (connectedUser instanceof UsernamePasswordAuthenticationToken) {
            UsernamePasswordAuthenticationToken authToken = (UsernamePasswordAuthenticationToken) connectedUser;
            Object principal = authToken.getPrincipal();

            response.put("principalType", principal.getClass().getSimpleName());
            response.put("username", connectedUser.getName());
            response.put("authorities", authToken.getAuthorities().stream()
                    .map(authority -> authority.getAuthority())
                    .toList());

            if (principal instanceof org.springframework.security.core.userdetails.UserDetails) {
                org.springframework.security.core.userdetails.UserDetails userDetails =
                    (org.springframework.security.core.userdetails.UserDetails) principal;
                response.put("enabled", userDetails.isEnabled());
                response.put("accountNonExpired", userDetails.isAccountNonExpired());
                response.put("accountNonLocked", userDetails.isAccountNonLocked());
                response.put("credentialsNonExpired", userDetails.isCredentialsNonExpired());
            }
        } else {
            response.put("principalType", connectedUser.getClass().getSimpleName());
            response.put("username", connectedUser.getName());
        }

        return ResponseEntity.ok(response);
    }














}
