package whitecape.tech.clientauth;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

@SpringBootApplication
@ComponentScan({"whitecape.tech.clientauth", "Config", "Model", "Controllers", "auth", "repositories", "token", "DriverProfile"})
@EntityScan({"Model", "token"})
@EnableJpaRepositories(basePackages = {"repositories", "token"})
public class ClientAuthApplication {

    public static void main(String[] args) {
        SpringApplication.run(ClientAuthApplication.class, args);
    }

}
