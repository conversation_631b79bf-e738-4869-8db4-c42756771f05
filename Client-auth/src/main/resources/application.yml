server:
  port: 8082

logging:
  level:
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE


# JWT Configuration
application:
  security:
    jwt:
      secret-key: e3cb03ba3e9a4f476883618e1cdde7143a6d9fde0af5a779668550c534f6c60e
      expiration: 86400000 # a day
      refresh-token:
        expiration: 604800000 # 7 days

# Alternative configuration under spring namespace for better compatibility
spring:
  application:
    name: auth-service
  main:
    allow-bean-definition-overriding: true  # Prevents bean conflicts
  # CORS configuration
  web:
    cors:
      allowed-origins: "http://localhost:3000"
      allowed-methods: GET,POST,PUT,DELETE,OPTIONS
      allowed-headers: "*"
      allow-credentials: true

  # Email configuration
  mail:
    host: smtp.gmail.com
    port: 587
    username: ${EMAIL_USERNAME:<EMAIL>}
    password: ${EMAIL_PASSWORD:bvcj gqym hubi fwcq}
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
  datasource:
    url: **********************************************
    username: chirine
    password: chirine
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        format_sql: true
        dialect: org.hibernate.dialect.PostgreSQLDialect
  # JWT properties under spring namespace



eureka:
  client:
    serviceUrl:
      defaultZone: http://localhost:8761/eureka/
    fetchRegistry: true
    registerWithEureka: true
    healthcheck:
      enabled: true
  instance:
    appName: auth-service
    instanceId: ${spring.application.name}:${spring.application.instance_id:${random.value}}
    preferIpAddress: true
    leaseRenewalIntervalInSeconds: 10
    leaseExpirationDurationInSeconds: 30

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always