 version: '3.8'

services:
  client-auth:
    build: .
    ports:
      - "8082:8082"
    environment:
      - SPRING_DATASOURCE_URL=*********************************************
      - SPRING_DATASOURCE_USERNAME=chirine
      - SPRING_DATASOURCE_PASSWORD=chirine
      - SPRING_JPA_HIBERNATE_DDL_AUTO=update
    depends_on:
      - postgres
    networks:
      - pfe-network
    healthcheck:
      test: ["CMD", "wget", "-q", "-T", "3", "-O", "/dev/null", "http://localhost:8082/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 40s

  postgres:
    image: postgres:14-alpine
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=auth-database
      - POSTGRES_USER=chirine
      - POSTGRES_PASSWORD=chirine
    volumes:
      - postgres-data:/var/lib/postgresql/data
    networks:
      - pfe-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U chirine -d auth-database"]
      interval: 10s
      timeout: 5s
      retries: 5

networks:
  pfe-network:
    driver: bridge

volumes:
  postgres-data:
