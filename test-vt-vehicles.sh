#!/bin/bash

echo "🧪 Testing VT Service vehicles endpoint..."

# You'll need to replace this with a real JWT token from your login
JWT_TOKEN="YOUR_JWT_TOKEN_HERE"

echo "🚗 Testing VT vehicles endpoint through API Gateway..."
curl -v -X GET "http://localhost:8080/api/v1/vt/vehicles" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json"

echo ""
echo "🔍 Testing VT vehicles endpoint directly..."
curl -v -X GET "http://localhost:8083/api/v1/vt/vehicles" \
  -H "X-User-Email: <EMAIL>" \
  -H "Content-Type: application/json"
