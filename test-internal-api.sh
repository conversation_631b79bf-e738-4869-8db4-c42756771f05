#!/bin/bash

echo "🧪 Testing Internal API directly..."

# Test internal API endpoint directly
echo "📞 Testing Client-auth internal API..."
curl -v -X GET "http://localhost:8082/api/v1/internal/driver/vehicles" \
  -H "X-User-Email: <EMAIL>" \
  -H "X-Service-Token: vt-service-internal-token" \
  -H "X-Requesting-Service: vt-service"

echo ""
echo "🌐 Testing through API Gateway..."
curl -v -X GET "http://localhost:8080/api/v1/internal/driver/vehicles" \
  -H "X-User-Email: <EMAIL>" \
  -H "X-Service-Token: vt-service-internal-token" \
  -H "X-Requesting-Service: vt-service"
